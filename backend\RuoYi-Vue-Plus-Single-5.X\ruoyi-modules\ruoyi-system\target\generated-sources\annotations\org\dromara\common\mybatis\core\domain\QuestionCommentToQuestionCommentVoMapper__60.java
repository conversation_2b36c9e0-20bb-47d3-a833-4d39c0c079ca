package org.dromara.common.mybatis.core.domain;

import io.github.linpeilie.AutoMapperConfig__138;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.bo.QuestionCommentBoToQuestionCommentMapper__63;
import org.dromara.system.domain.vo.QuestionCommentVo;
import org.dromara.system.domain.vo.QuestionCommentVoToQuestionCommentMapper__60;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__138.class,
    uses = {QuestionCommentBoToQuestionCommentMapper__63.class,QuestionCommentVoToQuestionCommentMapper__60.class},
    imports = {}
)
public interface QuestionCommentToQuestionCommentVoMapper__60 extends BaseMapper<QuestionComment, QuestionCommentVo> {
}
