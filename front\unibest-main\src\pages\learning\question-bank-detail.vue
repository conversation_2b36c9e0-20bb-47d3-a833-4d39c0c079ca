<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
// @ts-ignore
import HeadBar from '@/components/HeadBar.vue'
import {
  getQuestionBankDetail,
  toggleQuestionBankBookmarkNew,
  // @ts-ignore
} from '@/service/learning'
import { questionBankApi, type QuestionBank as QuestionBankType } from '@/service/questionBank'
import { questionApi, type Question as QuestionType } from '@/service/question'
import type {
  QuestionBank,
  // @ts-ignore
} from '@/types/learning'

/**
 * @description 题目接口定义 - 使用新的API类型
 */
interface Question extends QuestionType {
  id?: string // 兼容旧版本
  isCompleted?: boolean
}

/**
 * @description 筛选选项接口定义
 */
interface FilterOption {
  key: string
  label: string
  count: number
}

// 页面参数
const bankId = ref<string>('')
const bankTitle = ref<string>('')

// 题库信息
const questionBank = ref<QuestionBank>({
  id: '1',
  title: '数据结构与算法',
  majorId: '1',
  description: '包含栈、队列、树、图等核心数据结构题目',
  icon: '🌳',
  color: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
  difficulty: '中等',
  totalQuestions: 156,
  practiceCount: 1245,
  progress: 68,
  categories: ['算法', '数据结构', '编程'],
  isBookmarked: false,
})

// 筛选相关
const activeFilter = ref<string>('all')
const searchQuery = ref<string>('')

// 筛选选项
const filterOptions = ref<FilterOption[]>([
  { key: 'all', label: '全部题目', count: 0 },
  { key: 'easy', label: '简单', count: 0 },
  { key: 'medium', label: '中等', count: 0 },
  { key: 'hard', label: '困难', count: 0 },
  { key: 'completed', label: '已完成', count: 0 },
  { key: 'uncompleted', label: '未完成', count: 0 },
])

// 题目列表
const questions = ref<Question[]>([
  {
    id: '1',
    title: '两数之和',
    difficulty: '简单',
    tags: ['数组', '哈希表'],
    acceptanceRate: 85.2,
    isCompleted: true,
    practiceCount: 1200,
    correctRate: 85,
    commentCount: 45,
    category: '数组',
    description:
      '给定一个整数数组 nums 和一个整数目标值 target，请你在该数组中找出和为目标值 target 的那两个整数，并返回它们的数组下标。',
  },
  {
    id: '2',
    title: '有效的括号',
    difficulty: '简单',
    tags: ['栈', '字符串'],
    acceptanceRate: 78.5,
    isCompleted: false,
    practiceCount: 980,
    correctRate: 78,
    commentCount: 32,
    category: '栈',
    description:
      "给定一个只包括 '('，')' ，'{' ，'}' ，'[' ，']' 的字符串 s ，判断字符串是否有效。",
  },
  {
    id: '3',
    title: '二叉树的中序遍历',
    difficulty: '中等',
    tags: ['树', '深度优先搜索', '栈'],
    acceptanceRate: 65.4,
    isCompleted: true,
    practiceCount: 756,
    correctRate: 65,
    commentCount: 28,
    category: '树',
    description: '给定一个二叉树的根节点 root ，返回它的中序遍历。',
  },
  {
    id: '4',
    title: '最大子数组和',
    difficulty: '中等',
    tags: ['数组', '动态规划', '分治'],
    acceptanceRate: 58.7,
    isCompleted: false,
    practiceCount: 634,
    correctRate: 58,
    commentCount: 41,
    category: '动态规划',
    description:
      '给你一个整数数组 nums ，请你找出一个具有最大和的连续子数组（子数组最少包含一个元素），返回其最大和。',
  },
  {
    id: '5',
    title: '合并两个有序链表',
    difficulty: '简单',
    tags: ['链表', '递归'],
    acceptanceRate: 72.3,
    isCompleted: false,
    practiceCount: 523,
    correctRate: 72,
    commentCount: 19,
    category: '链表',
    description:
      '将两个升序链表合并为一个新的升序链表并返回。新链表是通过拼接给定的两个链表的所有节点组成的。',
  },
])

const totalQuestions = ref<number>(0)
const loading = ref<boolean>(false)

// 计算属性
/**
 * @description 根据搜索条件和筛选条件过滤题目列表
 * @returns 过滤后的题目列表
 */
const filteredQuestions = computed(() => {
  let filtered = questions.value

  // 根据搜索关键词筛选
  if (searchQuery.value.trim()) {
    const query = searchQuery.value.toLowerCase()
    filtered = filtered.filter(
      (question) =>
        question.title.toLowerCase().includes(query) ||
        (question.description && question.description.toLowerCase().includes(query)) ||
        (question.tags && question.tags.some((tag) => tag.toLowerCase().includes(query))),
    )
  }

  // 根据筛选条件筛选
  if (activeFilter.value !== 'all') {
    switch (activeFilter.value) {
      case 'easy':
        filtered = filtered.filter((question) => question.difficulty === '简单')
        break
      case 'medium':
        filtered = filtered.filter((question) => question.difficulty === '中等')
        break
      case 'hard':
        filtered = filtered.filter((question) => question.difficulty === '困难')
        break
      case 'completed':
        filtered = filtered.filter((question) => question.isCompleted === true)
        break
      case 'uncompleted':
        filtered = filtered.filter((question) => question.isCompleted === false)
        break
    }
  }

  return filtered
})

/**
 * @description 加载题库详情 - 使用新API
 */
const loadQuestionBankDetail = async (): Promise<void> => {
  try {
    // 使用新的API获取题库详情
    const response = await questionBankApi.getDetail(parseInt(bankId.value))
    if (response.code === 200) {
      questionBank.value = {
        ...response.data,
        id: response.data.bankId.toString(), // 兼容旧版本
      }
      bankTitle.value = response.data.title
    } else {
      uni.showToast({
        title: response.msg || '获取题库详情失败',
        icon: 'none',
      })
    }
  } catch (error) {
    console.error('获取题库详情失败：', error)
    uni.showToast({
      title: '获取题库详情失败',
      icon: 'none',
    })
  }
}

/**
 * @description 加载题目列表 - 使用新API
 */
const loadQuestionList = async (): Promise<void> => {
  try {
    const response = await questionApi.getListByBankId(parseInt(bankId.value), {
      page: 1,
      pageSize: 100, // 加载更多题目
    })
    if (response.code === 200) {
      // 转换数据格式以兼容现有组件
      questions.value = response.data.records.map(item => ({
        ...item,
        id: item.questionId.toString(), // 兼容旧版本
        difficulty: getDifficultyTextFromNumber(item.difficulty),
        isCompleted: false, // 默认未完成，可以根据用户数据调整
      }))
      updateFilterCounts()
    } else {
      uni.showToast({
        title: response.msg || '获取题目列表失败',
        icon: 'none',
      })
    }
  } catch (error) {
    console.error('获取题目列表失败：', error)
    uni.showToast({
      title: '获取题目列表失败',
      icon: 'none',
    })
  }
}

/**
 * @description 将数字难度转换为中文
 */
const getDifficultyTextFromNumber = (difficulty: number): string => {
  const map: Record<number, string> = {
    1: '简单',
    2: '中等',
    3: '困难',
  }
  return map[difficulty] || '未知'
}

/**
 * @description 更新筛选选项的计数
 */
const updateFilterCounts = (): void => {
  filterOptions.value.forEach((option) => {
    switch (option.key) {
      case 'all':
        option.count = questions.value.length
        break
      case 'easy':
        option.count = questions.value.filter((q) => q.difficulty === '简单').length
        break
      case 'medium':
        option.count = questions.value.filter((q) => q.difficulty === '中等').length
        break
      case 'hard':
        option.count = questions.value.filter((q) => q.difficulty === '困难').length
        break
      case 'completed':
        option.count = questions.value.filter((q) => q.isCompleted === true).length
        break
      case 'uncompleted':
        option.count = questions.value.filter((q) => q.isCompleted === false).length
        break
    }
  })
}

/**
 * @description 设置筛选条件
 * @param filterKey 筛选条件的key值
 */
const setFilter = (filterKey: string): void => {
  activeFilter.value = filterKey
  updateFilterCounts()
}

/**
 * @description 处理搜索
 */
const handleSearch = (): void => {
  // 这里可以添加搜索逻辑
  updateFilterCounts()
}

/**
 * @description 处理题目点击事件，跳转到题目详情页
 * @param question 被点击的题目对象
 */
const handleQuestionClick = (question: Question): void => {
  uni.navigateTo({
    url: `/pages/learning/question-detail?id=${question.id}&title=${encodeURIComponent(question.title)}`,
  })
}

/**
 * @description 获取难度等级的中文文本
 * @param difficulty 难度等级
 * @returns 难度等级中文文本
 */
const getDifficultyText = (difficulty: string): string => {
  return difficulty // 后端已返回中文
}

/**
 * @description 获取难度等级对应的颜色样式类
 * @param difficulty 难度等级
 * @returns 对应的CSS类名
 */
const getDifficultyColor = (difficulty: string): string => {
  const map: Record<string, string> = {
    简单: 'text-green-500',
    中等: 'text-yellow-500',
    困难: 'text-red-500',
  }
  return map[difficulty] || 'text-gray-500'
}

/**
 * @description 计算学习进度百分比
 * @returns 进度百分比数值
 */
const getProgressPercentage = (): number => {
  return questionBank.value?.progress || 0
}

/**
 * @description 切换收藏状态
 */
const handleToggleBookmark = async (): Promise<void> => {
  try {
    const response = await toggleQuestionBankBookmarkNew(bankId.value)
    if (response.code === 200) {
      if (questionBank.value) {
        questionBank.value.isBookmarked = response.data.isBookmarked
      }
      uni.showToast({
        title: response.data.message,
        icon: 'success',
      })
    } else {
      uni.showToast({
        title: response.message || '操作失败',
        icon: 'none',
      })
    }
  } catch (error) {
    console.error('切换收藏状态失败：', error)
    uni.showToast({
      title: '操作失败',
      icon: 'none',
    })
  }
}

/**
 * @description 获取页面参数的辅助函数
 * @returns 页面参数对象
 */
const getPageOptions = () => {
  // #ifdef H5
  // H5环境下从URL参数获取
  const urlParams = new URLSearchParams(window.location.search)
  return {
    id: urlParams.get('id') || '',
    title: urlParams.get('title') || '',
  }
  // #endif
}

// 生命周期
onMounted(() => {
  // 获取页面参数
  const options = getPageOptions()

  bankId.value = options.id || '1'
  bankTitle.value = options.title ? decodeURIComponent(options.title) : questionBank.value.title

  // 根据ID设置题库信息（这里使用模拟数据）
  questionBank.value.title = bankTitle.value

  // 加载数据
  loadQuestionBankDetail()
  loadQuestionList()

  // 初始化题目总数和筛选计数
  totalQuestions.value = questions.value.length
  updateFilterCounts()
})
</script>

<template>
  <view class="question-bank-detail-container bg-gray-50 min-h-screen">
    <!-- 头部导航 -->
    <HeadBar :title="bankTitle" />

    <!-- 主要内容 -->
    <scroll-view scroll-y class="main-content">
      <!-- 题库信息卡片 -->
      <view
        v-if="questionBank"
        class="bank-info-card bg-white mx-4 mt-4 rounded-xl overflow-hidden shadow-sm"
      >
        <!-- 头部 -->
        <view
          class="card-header bg-gradient-to-r p-4 text-white"
          :style="{ background: questionBank.color }"
        >
          <view class="flex items-center mb-3">
            <text class="text-3xl mr-4">{{ questionBank.icon }}</text>
            <view class="flex-1">
              <text class="text-xl font-bold block mb-1">{{ questionBank.title }}</text>
              <view class="flex items-center">
                <text class="text-sm opacity-90 mr-4">{{ questionBank.totalQuestions }}道题</text>
                <text class="text-sm opacity-90 mr-4">练习{{ questionBank.practiceCount }}次</text>
                <button
                  v-if="questionBank.isBookmarked"
                  class="bookmark-btn bg-yellow-500 px-2 py-1 rounded-full"
                  @click="handleToggleBookmark"
                >
                  <text class="text-xs text-white font-bold">已收藏</text>
                </button>
                <button
                  v-else
                  class="bookmark-btn bg-gray-500 px-2 py-1 rounded-full"
                  @click="handleToggleBookmark"
                >
                  <text class="text-xs text-white font-bold">收藏</text>
                </button>
              </view>
            </view>
          </view>
          <text class="text-sm opacity-90 leading-relaxed">{{ questionBank.description }}</text>
        </view>

        <!-- 进度信息 -->
        <view class="progress-info p-4">
          <view class="flex items-center justify-between mb-3">
            <text class="text-sm text-gray-600">学习进度</text>
            <text class="text-sm font-medium text-gray-800">{{ getProgressPercentage() }}%</text>
          </view>
          <view class="progress-bar bg-gray-200 rounded-full h-3 overflow-hidden">
            <view
              class="h-3 bg-blue-500 rounded-full transition-all duration-500"
              :style="{ width: getProgressPercentage() + '%' }"
            ></view>
          </view>
        </view>

        <!-- 标签 -->
        <view class="tags-section px-4 pb-4">
          <view class="flex flex-wrap gap-2">
            <view
              v-for="category in questionBank.categories"
              :key="category"
              class="tag bg-blue-50 text-blue-600 px-3 py-1 text-sm rounded-full"
            >
              {{ category }}
            </view>
          </view>
        </view>
      </view>

      <!-- 搜索和筛选 -->
      <view class="search-filter-section bg-white mx-4 mt-4 rounded-xl p-4">
        <!-- 搜索框 -->
        <view class="search-box mb-4">
          <view class="relative">
            <input
              v-model="searchQuery"
              class="search-input w-full bg-gray-50 border border-gray-200 rounded-lg px-4 py-3 pl-10 text-gray-800 placeholder-gray-500"
              placeholder="搜索题目、标签或关键词..."
              @confirm="handleSearch"
            />
            <view class="search-icon absolute left-3 top-1/2 transform -translate-y-1/2">
              <view class="i-fa-solid-search text-gray-400"></view>
            </view>
          </view>
        </view>

        <!-- 筛选选项 -->
        <view class="filter-options">
          <text class="text-sm font-bold text-gray-800 mb-3 block">筛选条件</text>
          <scroll-view scroll-x class="filter-scroll">
            <view class="flex space-x-3 pb-1">
              <button
                v-for="filter in filterOptions"
                :key="filter.key"
                class="filter-btn flex-shrink-0 px-4 py-2 rounded-lg border transition-all duration-200"
                :class="
                  activeFilter === filter.key
                    ? 'bg-blue-500 text-white border-blue-500'
                    : 'bg-gray-50 text-gray-600 border-gray-200'
                "
                @click="setFilter(filter.key)"
              >
                <text>{{ filter.label }}</text>
                <text v-if="filter.count > 0" class="ml-1 opacity-75">({{ filter.count }})</text>
              </button>
            </view>
          </scroll-view>
        </view>
      </view>

      <!-- 题目列表 -->
      <view class="questions-section mx-4 mt-4 pb-8">
        <view class="flex items-center justify-between mb-4">
          <text class="text-lg font-bold text-gray-800">题目列表</text>
          <text class="text-sm text-gray-500">共{{ totalQuestions }}道题</text>
        </view>

        <!-- 加载状态 -->
        <view v-if="loading" class="loading-state text-center py-8">
          <text class="text-gray-500">加载中...</text>
        </view>

        <!-- 题目列表 -->
        <view v-else class="questions-list space-y-3">
          <view
            v-for="(question, index) in filteredQuestions"
            :key="question.id"
            class="question-card bg-white rounded-lg p-4 border border-gray-200 transition-all duration-200"
            @click="handleQuestionClick(question)"
          >
            <view class="flex items-start justify-between">
              <view class="flex-1">
                <!-- 题目标题和状态 -->
                <view class="flex items-center mb-2">
                  <text class="question-number text-gray-400 text-sm mr-3 font-mono">
                    {{ index + 1 }}.
                  </text>
                  <view
                    v-if="question.isCompleted"
                    class="status-icon i-fa-solid-check-circle text-green-500 mr-2"
                  ></view>
                  <text class="question-title font-medium text-gray-800 text-base flex-1">
                    {{ question.title }}
                  </text>
                  <view
                    class="difficulty-badge px-2 py-1 rounded-full text-xs font-medium"
                    :class="getDifficultyColor(question.difficulty)"
                  >
                    {{ getDifficultyText(question.difficulty) }}
                  </view>
                </view>

                <!-- 题目描述 -->
                <text
                  v-if="question.description"
                  class="question-description text-sm text-gray-600 mb-3 block leading-relaxed"
                >
                  {{ question.description }}
                </text>

                <!-- 标签和通过率 -->
                <view class="flex items-center justify-between">
                  <view class="tags flex flex-wrap gap-1">
                    <view
                      v-for="tag in question.tags || []"
                      :key="tag"
                      class="tag bg-gray-100 text-gray-600 px-2 py-1 text-xs rounded"
                    >
                      {{ tag }}
                    </view>
                  </view>
                  <text
                    v-if="question.acceptanceRate"
                    class="acceptance-rate text-xs text-gray-500"
                  >
                    通过率 {{ question.acceptanceRate }}%
                  </text>
                </view>
              </view>

              <!-- 操作按钮 -->
              <view class="action-btn ml-4">
                <view class="i-fa-solid-chevron-right text-gray-400"></view>
              </view>
            </view>
          </view>
        </view>

        <!-- 空状态 -->
        <view
          v-if="!loading && filteredQuestions.length === 0"
          class="empty-state text-center py-12"
        >
          <view class="i-fa-solid-search text-4xl text-gray-300 mb-4"></view>
          <text class="text-gray-500 text-lg block mb-2">没有找到相关题目</text>
          <text class="text-gray-400 text-sm">试试调整搜索关键词或筛选条件</text>
        </view>
      </view>
    </scroll-view>
  </view>
</template>

<style scoped lang="scss">
.question-bank-detail-container {
  min-height: 100vh;
  background-color: #f9fafb;
}

.main-content {
  height: calc(100vh - 88px);
  padding-bottom: 20px;
}

// 题库信息卡片
.bank-info-card {
  .card-header {
    position: relative;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0) 100%);
      pointer-events: none;
    }
  }

  .hot-badge {
    animation: pulse 2s infinite;
  }

  .progress-bar {
    .h-3 {
      transition: width 0.5s ease;
    }
  }
}

// 搜索和筛选区域
.search-filter-section {
  .search-input {
    transition: all 0.2s ease;

    &:focus {
      border-color: #3b82f6;
      box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    }
  }

  .filter-btn {
    transition: all 0.2s ease;
    cursor: pointer;

    // #ifdef H5
    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
    // #endif
  }
}

// 题目卡片
.question-card {
  cursor: pointer;
  transition: all 0.2s ease;

  // #ifdef H5
  &:hover {
    border-color: #3b82f6;
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
    transform: translateY(-1px);
  }
  // #endif

  .question-number {
    min-width: 24px;
  }

  .question-title {
    line-height: 1.4;
  }

  .question-description {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .difficulty-badge {
    &.text-green-500 {
      background-color: #dcfce7;
      color: #16a34a;
    }

    &.text-yellow-500 {
      background-color: #fef3c7;
      color: #d97706;
    }

    &.text-red-500 {
      background-color: #fee2e2;
      color: #dc2626;
    }
  }

  .action-btn {
    transition: transform 0.2s ease;
  }

  // #ifdef H5
  &:hover .action-btn {
    transform: translateX(2px);
  }
  // #endif
}

// 空状态
.empty-state {
  .i-fa-solid-search {
    opacity: 0.3;
  }
}

// 动画
@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.8;
  }
}

// 间距调整
.space-y-3 > view:not(:first-child) {
  margin-top: 12px;
}

.space-x-3 > view:not(:first-child) {
  margin-left: 12px;
}

.gap-1 > view:not(:first-child) {
  margin-left: 4px;
}

.gap-2 > view:not(:first-child) {
  margin-left: 8px;
}

// 响应式调整
@media (max-width: 640px) {
  .question-card {
    .question-description {
      -webkit-line-clamp: 3;
    }
  }
}
</style>
