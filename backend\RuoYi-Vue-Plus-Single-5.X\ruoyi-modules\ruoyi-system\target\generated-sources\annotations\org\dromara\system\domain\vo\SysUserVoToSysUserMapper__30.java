package org.dromara.system.domain.vo;

import io.github.linpeilie.AutoMapperConfig__138;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.SysRoleToSysRoleVoMapper__30;
import org.dromara.system.domain.SysUser;
import org.dromara.system.domain.SysUserToSysUserVoMapper__30;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__138.class,
    uses = {SysRoleVoToSysRoleMapper__30.class,SysRoleToSysRoleVoMapper__30.class,SysUserToSysUserVoMapper__30.class},
    imports = {}
)
public interface SysUserVoToSysUserMapper__30 extends BaseMapper<SysUserVo, SysUser> {
}
