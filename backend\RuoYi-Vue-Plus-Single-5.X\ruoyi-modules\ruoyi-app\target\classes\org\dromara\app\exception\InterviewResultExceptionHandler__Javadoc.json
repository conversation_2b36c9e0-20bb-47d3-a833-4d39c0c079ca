{"doc": "\n 面试结果相关异常处理器\r\n\r\n <AUTHOR>\r\n @date 2025-07-17\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "handleServiceException", "paramTypes": ["org.dromara.common.core.exception.ServiceException"], "doc": "\n 处理业务异常\r\n"}, {"name": "handleMethodArgumentNotValidException", "paramTypes": ["org.springframework.web.bind.MethodArgumentNotValidException"], "doc": "\n 处理参数验证异常（@Valid）\r\n"}, {"name": "handleBindException", "paramTypes": ["org.springframework.validation.BindException"], "doc": "\n 处理参数绑定异常\r\n"}, {"name": "handleConstraintViolationException", "paramTypes": ["jakarta.validation.ConstraintViolationException"], "doc": "\n 处理约束验证异常（@Validated）\r\n"}, {"name": "handleIllegalArgumentException", "paramTypes": ["java.lang.IllegalArgumentException"], "doc": "\n 处理非法参数异常\r\n"}, {"name": "handleNullPointerException", "paramTypes": ["java.lang.NullPointerException"], "doc": "\n 处理空指针异常\r\n"}, {"name": "handleDataAccessException", "paramTypes": ["java.lang.Exception"], "doc": "\n 处理数据库相关异常\r\n"}, {"name": "handleException", "paramTypes": ["java.lang.Exception"], "doc": "\n 处理其他未知异常\r\n"}], "constructors": []}