package org.dromara.app.domain.vo;

import io.github.linpeilie.AutoMapperConfig__139;
import io.github.linpeilie.BaseMapper;
import org.dromara.app.domain.KnowledgeBase;
import org.dromara.app.domain.KnowledgeBaseToKnowledgeBaseVoMapper__5;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__139.class,
    uses = {KnowledgeBaseToKnowledgeBaseVoMapper__5.class},
    imports = {}
)
public interface KnowledgeBaseVoToKnowledgeBaseMapper__5 extends BaseMapper<KnowledgeBaseVo, KnowledgeBase> {
}
