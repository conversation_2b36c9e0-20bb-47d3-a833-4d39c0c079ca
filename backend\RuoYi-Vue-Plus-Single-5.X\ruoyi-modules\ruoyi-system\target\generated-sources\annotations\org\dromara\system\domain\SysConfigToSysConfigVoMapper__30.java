package org.dromara.system.domain;

import io.github.linpeilie.AutoMapperConfig__138;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.bo.SysConfigBoToSysConfigMapper__30;
import org.dromara.system.domain.vo.SysConfigVo;
import org.dromara.system.domain.vo.SysConfigVoToSysConfigMapper__30;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__138.class,
    uses = {SysConfigVoToSysConfigMapper__30.class,SysConfigBoToSysConfigMapper__30.class},
    imports = {}
)
public interface SysConfigToSysConfigVoMapper__30 extends BaseMapper<SysConfig, SysConfigVo> {
}
