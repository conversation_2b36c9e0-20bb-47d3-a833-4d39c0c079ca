package org.dromara.system.domain;

import io.github.linpeilie.AutoMapperConfig__139;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.bo.SysLogininforBoToSysLogininforMapper__5;
import org.dromara.system.domain.vo.SysLogininforVo;
import org.dromara.system.domain.vo.SysLogininforVoToSysLogininforMapper__5;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__139.class,
    uses = {SysLogininforBoToSysLogininforMapper__5.class,SysLogininforVoToSysLogininforMapper__5.class},
    imports = {}
)
public interface SysLogininforToSysLogininforVoMapper__5 extends BaseMapper<SysLogininfor, SysLogininforVo> {
}
