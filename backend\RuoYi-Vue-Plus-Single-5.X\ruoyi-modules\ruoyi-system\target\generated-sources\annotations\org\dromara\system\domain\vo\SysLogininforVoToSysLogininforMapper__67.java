package org.dromara.system.domain.vo;

import io.github.linpeilie.AutoMapperConfig__138;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.SysLogininfor;
import org.dromara.system.domain.SysLogininforToSysLogininforVoMapper__67;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__138.class,
    uses = {SysLogininforToSysLogininforVoMapper__67.class},
    imports = {}
)
public interface SysLogininforVoToSysLogininforMapper__67 extends BaseMapper<SysLogininforVo, SysLogininfor> {
}
