{"doc": " 题目评论Service接口\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "queryById", "paramTypes": ["java.lang.Long"], "doc": " 查询题目评论\n\n @param commentId 评论主键\n @return 题目评论\n"}, {"name": "queryList", "paramTypes": ["org.dromara.system.domain.bo.QuestionCommentBo"], "doc": " 查询题目评论列表\n\n @param bo 题目评论查询条件\n @return 题目评论集合\n"}, {"name": "queryPageList", "paramTypes": ["org.dromara.system.domain.bo.QuestionCommentBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 分页查询题目评论列表\n\n @param bo        题目评论查询条件\n @param pageQuery 分页参数\n @return 题目评论分页集合\n"}, {"name": "queryPageListByQuestionId", "paramTypes": ["java.lang.Long", "org.dromara.system.domain.bo.QuestionCommentBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 根据题目ID分页查询评论列表\n\n @param questionId 题目ID\n @param bo         评论查询条件\n @param pageQuery  分页参数\n @return 评论分页集合\n"}, {"name": "insertByBo", "paramTypes": ["org.dromara.system.domain.bo.QuestionCommentBo"], "doc": " 新增题目评论\n\n @param bo 题目评论信息\n @return 新增结果\n"}, {"name": "updateByBo", "paramTypes": ["org.dromara.system.domain.bo.QuestionCommentBo"], "doc": " 修改题目评论\n\n @param bo 题目评论信息\n @return 修改结果\n"}, {"name": "deleteWithValidByIds", "paramTypes": ["java.util.Collection"], "doc": " 校验并批量删除题目评论信息\n\n @param ids 需要删除的题目评论主键集合\n @return 删除结果\n"}, {"name": "queryByQuestionId", "paramTypes": ["java.lang.Long"], "doc": " 根据题目ID查询评论列表\n\n @param questionId 题目ID\n @return 评论集合\n"}, {"name": "queryByUserId", "paramTypes": ["java.lang.Long"], "doc": " 根据用户ID查询评论列表\n\n @param userId 用户ID\n @return 评论集合\n"}, {"name": "queryByParentId", "paramTypes": ["java.lang.Long"], "doc": " 根据父评论ID查询子评论列表\n\n @param parentId 父评论ID\n @return 子评论集合\n"}, {"name": "queryCommentTree", "paramTypes": ["java.lang.Long"], "doc": " 查询评论树形结构\n\n @param questionId 题目ID\n @return 评论树形集合\n"}, {"name": "queryHotComments", "paramTypes": ["java.lang.Long", "java.lang.Integer"], "doc": " 查询热门评论列表\n\n @param questionId 题目ID\n @param limit      限制数量\n @return 评论集合\n"}, {"name": "queryLatestComments", "paramTypes": ["java.lang.Long", "java.lang.Integer"], "doc": " 查询最新评论列表\n\n @param questionId 题目ID\n @param limit      限制数量\n @return 评论集合\n"}, {"name": "updateLikeCount", "paramTypes": ["java.lang.Long", "java.lang.Integer"], "doc": " 更新评论点赞数\n\n @param commentId 评论ID\n @param increment 增量（可为负数）\n @return 更新结果\n"}, {"name": "updateReplyCount", "paramTypes": ["java.lang.Long", "java.lang.Integer"], "doc": " 更新评论回复数\n\n @param commentId 评论ID\n @param increment 增量（可为负数）\n @return 更新结果\n"}, {"name": "likeComment", "paramTypes": ["java.lang.Long", "java.lang.Long"], "doc": " 点赞/取消点赞评论\n\n @param commentId 评论ID\n @param userId    用户ID\n @return 操作结果\n"}, {"name": "replyComment", "paramTypes": ["org.dromara.system.domain.bo.QuestionCommentBo"], "doc": " 回复评论\n\n @param bo 评论信息\n @return 回复结果\n"}, {"name": "deleteByQuestionId", "paramTypes": ["java.lang.Long"], "doc": " 根据题目ID删除评论\n\n @param questionId 题目ID\n @return 删除结果\n"}, {"name": "deleteByUserId", "paramTypes": ["java.lang.Long"], "doc": " 根据用户ID删除评论\n\n @param userId 用户ID\n @return 删除结果\n"}, {"name": "countByQuestionId", "paramTypes": ["java.lang.Long"], "doc": " 统计题目下的评论数量\n\n @param questionId 题目ID\n @return 评论数量\n"}, {"name": "countByUserId", "paramTypes": ["java.lang.Long"], "doc": " 统计用户的评论数量\n\n @param userId 用户ID\n @return 评论数量\n"}, {"name": "auditComment", "paramTypes": ["java.lang.Long", "java.lang.String"], "doc": " 审核评论\n\n @param commentId 评论ID\n @param status    审核状态\n @return 审核结果\n"}, {"name": "batchAuditComments", "paramTypes": ["java.util.Collection", "java.lang.String"], "doc": " 批量审核评论\n\n @param commentIds 评论ID集合\n @param status     审核状态\n @return 审核结果\n"}, {"name": "exportComment", "paramTypes": ["org.dromara.system.domain.bo.QuestionCommentBo"], "doc": " 导出评论数据\n\n @param bo 评论查询条件\n @return 评论集合\n"}], "constructors": []}