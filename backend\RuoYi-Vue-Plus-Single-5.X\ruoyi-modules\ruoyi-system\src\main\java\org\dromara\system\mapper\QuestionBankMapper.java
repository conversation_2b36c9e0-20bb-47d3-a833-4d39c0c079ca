package org.dromara.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.dromara.common.mybatis.annotation.DataPermission;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.system.domain.bo.QuestionBankBo;
import org.dromara.system.domain.vo.QuestionBankVo;
import org.dromara.app.domain.QuestionBank;

import java.util.Collection;
import java.util.List;

/**
 * 题库Mapper接口
 *
 * <AUTHOR>
 */
public interface QuestionBankMapper extends BaseMapper<QuestionBank> {

    /**
     * 查询题库
     *
     * @param bankId 题库主键
     * @return 题库
     */
    QuestionBankVo selectVoById(Long bankId);

    /**
     * 查询题库列表
     *
     * @param questionBank 题库
     * @return 题库集合
     */
    @DataPermission(key = "bankId")
    List<QuestionBankVo> selectVoList(QuestionBankBo questionBank);

    /**
     * 分页查询题库列表
     *
     * @param page         分页参数
     * @param questionBank 题库查询条件
     * @return 题库集合
     */
    @DataPermission(key = "bankId")
    Page<QuestionBankVo> selectVoPage(Page<QuestionBank> page, @Param("ew") QuestionBankBo questionBank);

    /**
     * 根据条件分页查询题库列表
     *
     * @param pageQuery    分页参数
     * @param questionBank 题库查询条件
     * @return 题库集合
     */
    @DataPermission(key = "bankId")
    Page<QuestionBankVo> selectPageQuestionBankList(@Param("page") PageQuery pageQuery, @Param("questionBank") QuestionBankBo questionBank);

    /**
     * 查询题库列表
     *
     * @param bankIds 题库主键集合
     * @return 题库集合
     */
    List<QuestionBankVo> selectVoByIds(@Param("bankIds") Collection<Long> bankIds);

    /**
     * 根据题库编码查询题库
     *
     * @param bankCode 题库编码
     * @return 题库
     */
    QuestionBankVo selectVoByBankCode(@Param("bankCode") String bankCode);

    /**
     * 根据专业ID查询题库列表
     *
     * @param majorId 专业ID
     * @return 题库集合
     */
    List<QuestionBankVo> selectVoByMajorId(@Param("majorId") Long majorId);

    /**
     * 查询用户收藏的题库列表
     *
     * @param userId 用户ID
     * @return 题库集合
     */
    List<QuestionBankVo> selectBookmarkedBanksByUserId(@Param("userId") Long userId);

    /**
     * 查询热门题库列表
     *
     * @param limit 限制数量
     * @return 题库集合
     */
    List<QuestionBankVo> selectHotQuestionBanks(@Param("limit") Integer limit);

    /**
     * 更新题库练习次数
     *
     * @param bankId 题库ID
     * @return 影响行数
     */
    int updatePracticeCount(@Param("bankId") Long bankId);

    /**
     * 更新题库题目总数
     *
     * @param bankId 题库ID
     * @return 影响行数
     */
    int updateTotalQuestions(@Param("bankId") Long bankId);

    /**
     * 批量删除题库
     *
     * @param bankIds 需要删除的题库主键集合
     * @return 影响行数
     */
    int deleteQuestionBankByIds(@Param("bankIds") Collection<Long> bankIds);

    /**
     * 检查题库编码是否唯一
     *
     * @param bankCode 题库编码
     * @param bankId   题库ID（编辑时排除自己）
     * @return 数量
     */
    int checkBankCodeUnique(@Param("bankCode") String bankCode, @Param("bankId") Long bankId);
}
