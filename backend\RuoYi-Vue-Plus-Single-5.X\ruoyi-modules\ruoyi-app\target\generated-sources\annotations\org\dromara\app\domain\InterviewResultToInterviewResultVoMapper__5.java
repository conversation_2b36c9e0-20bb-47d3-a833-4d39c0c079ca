package org.dromara.app.domain;

import io.github.linpeilie.AutoMapperConfig__139;
import io.github.linpeilie.BaseMapper;
import org.dromara.app.domain.bo.InterviewResultBoToInterviewResultMapper__5;
import org.dromara.app.domain.vo.InterviewResultVo;
import org.dromara.app.domain.vo.InterviewResultVoToInterviewResultMapper__5;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__139.class,
    uses = {InterviewResultVoToInterviewResultMapper__5.class,InterviewResultBoToInterviewResultMapper__5.class},
    imports = {}
)
public interface InterviewResultToInterviewResultVoMapper__5 extends BaseMapper<InterviewResult, InterviewResultVo> {
}
