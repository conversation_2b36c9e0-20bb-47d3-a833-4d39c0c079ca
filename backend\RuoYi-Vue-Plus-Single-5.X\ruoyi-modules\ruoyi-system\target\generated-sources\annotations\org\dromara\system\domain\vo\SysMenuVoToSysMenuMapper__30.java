package org.dromara.system.domain.vo;

import io.github.linpeilie.AutoMapperConfig__138;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.SysMenu;
import org.dromara.system.domain.SysMenuToSysMenuVoMapper__30;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__138.class,
    uses = {SysMenuToSysMenuVoMapper__30.class,SysMenuToSysMenuVoMapper__30.class},
    imports = {}
)
public interface SysMenuVoToSysMenuMapper__30 extends BaseMapper<SysMenuVo, SysMenu> {
}
