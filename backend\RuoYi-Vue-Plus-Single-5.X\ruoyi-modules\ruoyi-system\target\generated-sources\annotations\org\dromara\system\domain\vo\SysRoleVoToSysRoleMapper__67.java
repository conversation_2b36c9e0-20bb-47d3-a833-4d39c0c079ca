package org.dromara.system.domain.vo;

import io.github.linpeilie.AutoMapperConfig__138;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.SysRole;
import org.dromara.system.domain.SysRoleToSysRoleVoMapper__67;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__138.class,
    uses = {SysRoleToSysRoleVoMapper__67.class},
    imports = {}
)
public interface SysRoleVoToSysRoleMapper__67 extends BaseMapper<SysRoleVo, SysRole> {
}
