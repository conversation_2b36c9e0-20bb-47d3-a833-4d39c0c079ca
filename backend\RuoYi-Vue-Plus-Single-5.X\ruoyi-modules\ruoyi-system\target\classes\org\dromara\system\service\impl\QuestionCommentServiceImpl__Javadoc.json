{"doc": " 题目评论Service业务层处理\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "queryById", "paramTypes": ["java.lang.Long"], "doc": " 查询题目评论\n"}, {"name": "queryList", "paramTypes": ["org.dromara.system.domain.bo.QuestionCommentBo"], "doc": " 查询题目评论列表\n"}, {"name": "queryPageList", "paramTypes": ["org.dromara.system.domain.bo.QuestionCommentBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 分页查询题目评论列表\n"}, {"name": "queryPageListByQuestionId", "paramTypes": ["java.lang.Long", "org.dromara.system.domain.bo.QuestionCommentBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 根据题目ID分页查询评论列表\n"}, {"name": "buildQueryWrapper", "paramTypes": ["org.dromara.system.domain.bo.QuestionCommentBo"], "doc": " 构建查询条件\n"}, {"name": "insertByBo", "paramTypes": ["org.dromara.system.domain.bo.QuestionCommentBo"], "doc": " 新增题目评论\n"}, {"name": "updateByBo", "paramTypes": ["org.dromara.system.domain.bo.QuestionCommentBo"], "doc": " 修改题目评论\n"}, {"name": "validEntityBeforeSave", "paramTypes": ["QuestionComment"], "doc": " 保存前的数据校验\n"}, {"name": "deleteWithValidByIds", "paramTypes": ["java.util.Collection"], "doc": " 批量删除题目评论\n"}, {"name": "queryByQuestionId", "paramTypes": ["java.lang.Long"], "doc": " 根据题目ID查询评论列表\n"}, {"name": "queryByUserId", "paramTypes": ["java.lang.Long"], "doc": " 根据用户ID查询评论列表\n"}, {"name": "queryByParentId", "paramTypes": ["java.lang.Long"], "doc": " 根据父评论ID查询子评论列表\n"}, {"name": "queryCommentTree", "paramTypes": ["java.lang.Long"], "doc": " 查询评论树形结构\n"}, {"name": "queryHotComments", "paramTypes": ["java.lang.Long", "java.lang.Integer"], "doc": " 查询热门评论列表\n"}, {"name": "queryLatestComments", "paramTypes": ["java.lang.Long", "java.lang.Integer"], "doc": " 查询最新评论列表\n"}, {"name": "updateLikeCount", "paramTypes": ["java.lang.Long", "java.lang.Integer"], "doc": " 更新评论点赞数\n"}, {"name": "updateReplyCount", "paramTypes": ["java.lang.Long", "java.lang.Integer"], "doc": " 更新评论回复数\n"}, {"name": "likeComment", "paramTypes": ["java.lang.Long", "java.lang.Long"], "doc": " 点赞/取消点赞评论\n"}, {"name": "replyComment", "paramTypes": ["org.dromara.system.domain.bo.QuestionCommentBo"], "doc": " 回复评论\n"}, {"name": "deleteByQuestionId", "paramTypes": ["java.lang.Long"], "doc": " 根据题目ID删除评论\n"}, {"name": "deleteByUserId", "paramTypes": ["java.lang.Long"], "doc": " 根据用户ID删除评论\n"}, {"name": "countByQuestionId", "paramTypes": ["java.lang.Long"], "doc": " 统计题目下的评论数量\n"}, {"name": "countByUserId", "paramTypes": ["java.lang.Long"], "doc": " 统计用户的评论数量\n"}, {"name": "auditComment", "paramTypes": ["java.lang.Long", "java.lang.String"], "doc": " 审核评论\n"}, {"name": "batchAuditComments", "paramTypes": ["java.util.Collection", "java.lang.String"], "doc": " 批量审核评论\n"}, {"name": "exportComment", "paramTypes": ["org.dromara.system.domain.bo.QuestionCommentBo"], "doc": " 导出评论数据\n"}], "constructors": []}