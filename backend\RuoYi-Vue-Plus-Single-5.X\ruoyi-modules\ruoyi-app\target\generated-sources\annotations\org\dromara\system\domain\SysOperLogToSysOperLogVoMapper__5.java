package org.dromara.system.domain;

import io.github.linpeilie.AutoMapperConfig__139;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.bo.SysOperLogBoToSysOperLogMapper__5;
import org.dromara.system.domain.vo.SysOperLogVo;
import org.dromara.system.domain.vo.SysOperLogVoToSysOperLogMapper__5;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__139.class,
    uses = {SysOperLogBoToSysOperLogMapper__5.class,SysOperLogVoToSysOperLogMapper__5.class},
    imports = {}
)
public interface SysOperLogToSysOperLogVoMapper__5 extends BaseMapper<SysOperLog, SysOperLogVo> {
}
