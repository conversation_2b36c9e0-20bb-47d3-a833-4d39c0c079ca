package org.dromara.system.domain.vo;

import io.github.linpeilie.AutoMapperConfig__138;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.SysSocial;
import org.dromara.system.domain.SysSocialToSysSocialVoMapper__67;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__138.class,
    uses = {SysSocialToSysSocialVoMapper__67.class},
    imports = {}
)
public interface SysSocialVoToSysSocialMapper__67 extends BaseMapper<SysSocialVo, SysSocial> {
}
