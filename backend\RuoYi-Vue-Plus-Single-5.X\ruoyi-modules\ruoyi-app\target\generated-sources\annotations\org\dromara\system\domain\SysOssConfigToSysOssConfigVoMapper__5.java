package org.dromara.system.domain;

import io.github.linpeilie.AutoMapperConfig__139;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.bo.SysOssConfigBoToSysOssConfigMapper__5;
import org.dromara.system.domain.vo.SysOssConfigVo;
import org.dromara.system.domain.vo.SysOssConfigVoToSysOssConfigMapper__5;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__139.class,
    uses = {SysOssConfigVoToSysOssConfigMapper__5.class,SysOssConfigBoToSysOssConfigMapper__5.class},
    imports = {}
)
public interface SysOssConfigToSysOssConfigVoMapper__5 extends BaseMapper<SysOssConfig, SysOssConfigVo> {
}
