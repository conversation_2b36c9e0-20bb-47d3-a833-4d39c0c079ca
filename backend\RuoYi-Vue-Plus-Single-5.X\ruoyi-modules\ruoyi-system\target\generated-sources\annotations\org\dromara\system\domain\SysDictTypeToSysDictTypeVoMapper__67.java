package org.dromara.system.domain;

import io.github.linpeilie.AutoMapperConfig__138;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.bo.SysDictTypeBoToSysDictTypeMapper__67;
import org.dromara.system.domain.vo.SysDictTypeVo;
import org.dromara.system.domain.vo.SysDictTypeVoToSysDictTypeMapper__67;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__138.class,
    uses = {SysDictTypeBoToSysDictTypeMapper__67.class,SysDictTypeVoToSysDictTypeMapper__67.class},
    imports = {}
)
public interface SysDictTypeToSysDictTypeVoMapper__67 extends BaseMapper<SysDictType, SysDictTypeVo> {
}
