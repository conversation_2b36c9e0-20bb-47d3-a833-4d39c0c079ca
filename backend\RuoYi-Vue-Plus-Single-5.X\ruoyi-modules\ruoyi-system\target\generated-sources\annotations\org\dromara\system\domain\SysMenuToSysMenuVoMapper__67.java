package org.dromara.system.domain;

import io.github.linpeilie.AutoMapperConfig__138;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.bo.SysMenuBoToSysMenuMapper__67;
import org.dromara.system.domain.vo.SysMenuVo;
import org.dromara.system.domain.vo.SysMenuVoToSysMenuMapper__67;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__138.class,
    uses = {SysMenuVoToSysMenuMapper__67.class,SysMenuBoToSysMenuMapper__67.class},
    imports = {}
)
public interface SysMenuToSysMenuVoMapper__67 extends BaseMapper<SysMenu, SysMenuVo> {
}
