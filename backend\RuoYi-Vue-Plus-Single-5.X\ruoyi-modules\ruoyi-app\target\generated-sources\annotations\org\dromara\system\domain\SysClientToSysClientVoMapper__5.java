package org.dromara.system.domain;

import io.github.linpeilie.AutoMapperConfig__139;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.bo.SysClientBoToSysClientMapper__5;
import org.dromara.system.domain.vo.SysClientVo;
import org.dromara.system.domain.vo.SysClientVoToSysClientMapper__5;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__139.class,
    uses = {SysClientVoToSysClientMapper__5.class,SysClientBoToSysClientMapper__5.class},
    imports = {}
)
public interface SysClientToSysClientVoMapper__5 extends BaseMapper<SysClient, SysClientVo> {
}
