package org.dromara.app.domain;

import io.github.linpeilie.AutoMapperConfig__139;
import io.github.linpeilie.BaseMapper;
import org.dromara.app.domain.vo.VideoDetailVo;
import org.dromara.app.domain.vo.VideoDetailVoToVideoMapper__5;
import org.dromara.app.utils.VideoMappingUtils;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__139.class,
    uses = {VideoMappingUtils.class,VideoDetailVoToVideoMapper__5.class},
    imports = {}
)
public interface VideoToVideoDetailVoMapper__5 extends BaseMapper<Video, VideoDetailVo> {
}
