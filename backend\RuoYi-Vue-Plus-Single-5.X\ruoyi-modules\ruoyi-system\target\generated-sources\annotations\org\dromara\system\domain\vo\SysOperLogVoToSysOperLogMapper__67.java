package org.dromara.system.domain.vo;

import io.github.linpeilie.AutoMapperConfig__138;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.SysOperLog;
import org.dromara.system.domain.SysOperLogToSysOperLogVoMapper__67;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__138.class,
    uses = {SysOperLogToSysOperLogVoMapper__67.class},
    imports = {}
)
public interface SysOperLogVoToSysOperLogMapper__67 extends BaseMapper<SysOperLogVo, SysOperLog> {
}
