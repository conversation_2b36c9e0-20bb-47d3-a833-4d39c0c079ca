package org.dromara.system.domain.vo;

import io.github.linpeilie.AutoMapperConfig__138;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.SysPost;
import org.dromara.system.domain.SysPostToSysPostVoMapper__30;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__138.class,
    uses = {SysPostToSysPostVoMapper__30.class},
    imports = {}
)
public interface SysPostVoToSysPostMapper__30 extends BaseMapper<SysPostVo, SysPost> {
}
