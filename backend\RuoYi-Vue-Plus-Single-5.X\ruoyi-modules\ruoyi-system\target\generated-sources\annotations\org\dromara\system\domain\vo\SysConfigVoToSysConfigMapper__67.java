package org.dromara.system.domain.vo;

import io.github.linpeilie.AutoMapperConfig__138;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.SysConfig;
import org.dromara.system.domain.SysConfigToSysConfigVoMapper__67;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__138.class,
    uses = {SysConfigToSysConfigVoMapper__67.class},
    imports = {}
)
public interface SysConfigVoToSysConfigMapper__67 extends BaseMapper<SysConfigVo, SysConfig> {
}
