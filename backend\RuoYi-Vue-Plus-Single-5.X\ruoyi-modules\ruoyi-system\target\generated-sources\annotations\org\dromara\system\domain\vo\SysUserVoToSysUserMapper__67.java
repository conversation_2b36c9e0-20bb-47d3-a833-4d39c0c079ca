package org.dromara.system.domain.vo;

import io.github.linpeilie.AutoMapperConfig__138;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.SysRoleToSysRoleVoMapper__67;
import org.dromara.system.domain.SysUser;
import org.dromara.system.domain.SysUserToSysUserVoMapper__67;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__138.class,
    uses = {SysRoleVoToSysRoleMapper__67.class,SysRoleToSysRoleVoMapper__67.class,SysUserToSysUserVoMapper__67.class},
    imports = {}
)
public interface SysUserVoToSysUserMapper__67 extends BaseMapper<SysUserVo, SysUser> {
}
