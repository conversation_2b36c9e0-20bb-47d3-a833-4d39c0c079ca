package org.dromara.system.domain;

import io.github.linpeilie.AutoMapperConfig__138;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.bo.SysOssBoToSysOssMapper__67;
import org.dromara.system.domain.vo.SysOssVo;
import org.dromara.system.domain.vo.SysOssVoToSysOssMapper__67;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__138.class,
    uses = {SysOssBoToSysOssMapper__67.class,SysOssVoToSysOssMapper__67.class},
    imports = {}
)
public interface SysOssToSysOssVoMapper__67 extends BaseMapper<SysOss, SysOssVo> {
}
