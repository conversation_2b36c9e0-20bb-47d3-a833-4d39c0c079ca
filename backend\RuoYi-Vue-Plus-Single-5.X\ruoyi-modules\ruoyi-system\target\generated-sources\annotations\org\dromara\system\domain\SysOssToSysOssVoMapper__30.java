package org.dromara.system.domain;

import io.github.linpeilie.AutoMapperConfig__138;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.bo.SysOssBoToSysOssMapper__30;
import org.dromara.system.domain.vo.SysOssVo;
import org.dromara.system.domain.vo.SysOssVoToSysOssMapper__30;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__138.class,
    uses = {SysOssBoToSysOssMapper__30.class,SysOssVoToSysOssMapper__30.class},
    imports = {}
)
public interface SysOssToSysOssVoMapper__30 extends BaseMapper<SysOss, SysOssVo> {
}
