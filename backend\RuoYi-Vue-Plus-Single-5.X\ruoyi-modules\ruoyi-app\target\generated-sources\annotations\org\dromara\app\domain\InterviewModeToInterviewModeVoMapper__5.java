package org.dromara.app.domain;

import io.github.linpeilie.AutoMapperConfig__139;
import io.github.linpeilie.BaseMapper;
import org.dromara.app.domain.vo.InterviewModeVo;
import org.dromara.app.domain.vo.InterviewModeVoToInterviewModeMapper__5;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__139.class,
    uses = {InterviewModeVoToInterviewModeMapper__5.class},
    imports = {}
)
public interface InterviewModeToInterviewModeVoMapper__5 extends BaseMapper<InterviewMode, InterviewModeVo> {
}
