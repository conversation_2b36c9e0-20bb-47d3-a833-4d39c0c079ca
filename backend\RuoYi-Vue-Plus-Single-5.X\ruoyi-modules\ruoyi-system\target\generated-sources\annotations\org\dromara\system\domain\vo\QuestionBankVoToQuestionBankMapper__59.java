package org.dromara.system.domain.vo;

import io.github.linpeilie.AutoMapperConfig__138;
import io.github.linpeilie.BaseMapper;
import org.dromara.common.mybatis.core.domain.QuestionBank;
import org.dromara.common.mybatis.core.domain.QuestionBankToQuestionBankVoMapper__59;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__138.class,
    uses = {QuestionBankToQuestionBankVoMapper__59.class},
    imports = {}
)
public interface QuestionBankVoToQuestionBankMapper__59 extends BaseMapper<QuestionBankVo, QuestionBank> {
}
