package org.dromara.app.domain.vo;

import io.github.linpeilie.AutoMapperConfig__139;
import io.github.linpeilie.BaseMapper;
import org.dromara.app.domain.InterviewResult;
import org.dromara.app.domain.InterviewResultToInterviewResultVoMapper__5;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__139.class,
    uses = {InterviewResultToInterviewResultVoMapper__5.class},
    imports = {}
)
public interface InterviewResultVoToInterviewResultMapper__5 extends BaseMapper<InterviewResultVo, InterviewResult> {
}
