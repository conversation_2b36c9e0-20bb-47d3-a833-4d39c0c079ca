package org.dromara.system.domain;

import io.github.linpeilie.AutoMapperConfig__138;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.bo.SysConfigBoToSysConfigMapper__67;
import org.dromara.system.domain.vo.SysConfigVo;
import org.dromara.system.domain.vo.SysConfigVoToSysConfigMapper__67;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__138.class,
    uses = {SysConfigVoToSysConfigMapper__67.class,SysConfigBoToSysConfigMapper__67.class},
    imports = {}
)
public interface SysConfigToSysConfigVoMapper__67 extends BaseMapper<SysConfig, SysConfigVo> {
}
