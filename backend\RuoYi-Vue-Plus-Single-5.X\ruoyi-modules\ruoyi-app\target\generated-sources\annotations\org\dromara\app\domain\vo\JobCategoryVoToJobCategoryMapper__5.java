package org.dromara.app.domain.vo;

import io.github.linpeilie.AutoMapperConfig__139;
import io.github.linpeilie.BaseMapper;
import org.dromara.app.domain.JobCategory;
import org.dromara.app.domain.JobCategoryToJobCategoryVoMapper__5;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__139.class,
    uses = {JobCategoryToJobCategoryVoMapper__5.class},
    imports = {}
)
public interface JobCategoryVoToJobCategoryMapper__5 extends BaseMapper<JobCategoryVo, JobCategory> {
}
