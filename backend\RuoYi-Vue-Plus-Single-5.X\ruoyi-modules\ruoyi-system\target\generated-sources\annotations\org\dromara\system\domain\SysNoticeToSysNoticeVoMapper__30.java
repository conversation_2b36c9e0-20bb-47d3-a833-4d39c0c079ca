package org.dromara.system.domain;

import io.github.linpeilie.AutoMapperConfig__138;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.bo.SysNoticeBoToSysNoticeMapper__30;
import org.dromara.system.domain.vo.SysNoticeVo;
import org.dromara.system.domain.vo.SysNoticeVoToSysNoticeMapper__30;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__138.class,
    uses = {SysNoticeVoToSysNoticeMapper__30.class,SysNoticeBoToSysNoticeMapper__30.class},
    imports = {}
)
public interface SysNoticeToSysNoticeVoMapper__30 extends BaseMapper<SysNotice, SysNoticeVo> {
}
