package org.dromara.system.domain;

import io.github.linpeilie.AutoMapperConfig__138;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.bo.SysLogininforBoToSysLogininforMapper__30;
import org.dromara.system.domain.vo.SysLogininforVo;
import org.dromara.system.domain.vo.SysLogininforVoToSysLogininforMapper__30;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__138.class,
    uses = {SysLogininforBoToSysLogininforMapper__30.class,SysLogininforVoToSysLogininforMapper__30.class},
    imports = {}
)
public interface SysLogininforToSysLogininforVoMapper__30 extends BaseMapper<SysLogininfor, SysLogininforVo> {
}
