package org.dromara.system.domain.bo;

import java.util.LinkedHashMap;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.dromara.common.mybatis.core.domain.QuestionComment;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-01T22:58:35+0800",
    comments = "version: 1.5.5.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250729-0351, environment: Java 21.0.8 (Eclipse Adoptium)"
)
@Component
public class QuestionCommentBoToQuestionCommentMapper__63Impl implements QuestionCommentBoToQuestionCommentMapper__63 {

    @Override
    public QuestionComment convert(QuestionCommentBo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        QuestionComment questionComment = new QuestionComment();

        questionComment.setCreateBy( arg0.getCreateBy() );
        questionComment.setCreateDept( arg0.getCreateDept() );
        questionComment.setCreateTime( arg0.getCreateTime() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            questionComment.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        questionComment.setSearchValue( arg0.getSearchValue() );
        questionComment.setUpdateBy( arg0.getUpdateBy() );
        questionComment.setUpdateTime( arg0.getUpdateTime() );
        questionComment.setCommentId( arg0.getCommentId() );
        questionComment.setContent( arg0.getContent() );
        questionComment.setIpAddress( arg0.getIpAddress() );
        questionComment.setLikeCount( arg0.getLikeCount() );
        questionComment.setParentId( arg0.getParentId() );
        questionComment.setQuestionId( arg0.getQuestionId() );
        questionComment.setRemark( arg0.getRemark() );
        questionComment.setReplyCount( arg0.getReplyCount() );
        questionComment.setSort( arg0.getSort() );
        questionComment.setStatus( arg0.getStatus() );
        questionComment.setUserId( arg0.getUserId() );

        return questionComment;
    }

    @Override
    public QuestionComment convert(QuestionCommentBo arg0, QuestionComment arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setCreateBy( arg0.getCreateBy() );
        arg1.setCreateDept( arg0.getCreateDept() );
        arg1.setCreateTime( arg0.getCreateTime() );
        if ( arg1.getParams() != null ) {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.getParams().clear();
                arg1.getParams().putAll( map );
            }
            else {
                arg1.setParams( null );
            }
        }
        else {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.setParams( new LinkedHashMap<String, Object>( map ) );
            }
        }
        arg1.setSearchValue( arg0.getSearchValue() );
        arg1.setUpdateBy( arg0.getUpdateBy() );
        arg1.setUpdateTime( arg0.getUpdateTime() );
        arg1.setCommentId( arg0.getCommentId() );
        arg1.setContent( arg0.getContent() );
        arg1.setIpAddress( arg0.getIpAddress() );
        arg1.setLikeCount( arg0.getLikeCount() );
        arg1.setParentId( arg0.getParentId() );
        arg1.setQuestionId( arg0.getQuestionId() );
        arg1.setRemark( arg0.getRemark() );
        arg1.setReplyCount( arg0.getReplyCount() );
        arg1.setSort( arg0.getSort() );
        arg1.setStatus( arg0.getStatus() );
        arg1.setUserId( arg0.getUserId() );

        return arg1;
    }
}
