package org.dromara.system.domain;

import io.github.linpeilie.AutoMapperConfig__138;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.bo.SysSocialBoToSysSocialMapper__67;
import org.dromara.system.domain.vo.SysSocialVo;
import org.dromara.system.domain.vo.SysSocialVoToSysSocialMapper__67;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__138.class,
    uses = {SysSocialBoToSysSocialMapper__67.class,SysSocialVoToSysSocialMapper__67.class},
    imports = {}
)
public interface SysSocialToSysSocialVoMapper__67 extends BaseMapper<SysSocial, SysSocialVo> {
}
