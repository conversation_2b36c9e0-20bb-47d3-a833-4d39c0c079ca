{"doc": "\n 成就系统控制器\r\n\r\n <AUTHOR>\r\n @date 2025-07-18\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "getAllBadges", "paramTypes": ["java.lang.String", "java.lang.Bo<PERSON>an", "java.lang.String"], "doc": "\n 获取用户所有徽章\r\n"}, {"name": "getBadgeDetail", "paramTypes": ["java.lang.String"], "doc": "\n 获取徽章详情\r\n"}, {"name": "pinBadge", "paramTypes": ["org.dromara.app.controller.achievement.AchievementController.PinBadgeRequest"], "doc": "\n 设置徽章置顶状态\r\n"}, {"name": "getPinnedBadges", "paramTypes": [], "doc": "\n 获取置顶徽章列表\r\n"}, {"name": "getAchievementStats", "paramTypes": [], "doc": "\n 获取成就统计信息\r\n"}, {"name": "getRecentAchievements", "paramTypes": ["java.lang.Integer"], "doc": "\n 获取最近解锁的成就\r\n"}, {"name": "getCategories", "paramTypes": [], "doc": "\n 获取成就分类列表\r\n"}, {"name": "getInProgressAchievements", "paramTypes": [], "doc": "\n 查询用户进行中的成就\r\n"}, {"name": "getAchievementDetail", "paramTypes": ["java.lang.String"], "doc": "\n 获取用户成就详情\r\n"}, {"name": "checkAchievements", "paramTypes": [], "doc": "\n 手动检查成就进度\r\n"}, {"name": "shareAchievements", "paramTypes": ["org.dromara.app.controller.achievement.AchievementController.ShareRequest"], "doc": "\n 分享成就墙\r\n"}, {"name": "getRecommendedAchievements", "paramTypes": ["java.lang.Integer"], "doc": "\n 获取推荐成就\r\n"}, {"name": "getLeaderboard", "paramTypes": ["java.lang.String", "java.lang.Integer"], "doc": "\n 获取成就排行榜\r\n"}, {"name": "getUserRanking", "paramTypes": ["java.lang.String"], "doc": "\n 获取用户排名信息\r\n"}, {"name": "recordEvent", "paramTypes": ["org.dromara.app.controller.achievement.AchievementController.EventRequest"], "doc": "\n 记录用户行为事件\r\n"}, {"name": "getUserAchievementCompletion", "paramTypes": [], "doc": "\n 获取用户成就完成度\r\n"}, {"name": "getEventStatistics", "paramTypes": ["java.lang.String", "java.lang.Integer"], "doc": "\n 获取事件统计信息\r\n"}, {"name": "unlockAchievement", "paramTypes": ["org.dromara.app.controller.achievement.AchievementController.UnlockRequest"], "doc": "\n 手动解锁成就\r\n"}, {"name": "recalculateUserProgress", "paramTypes": [], "doc": "\n 重新计算用户进度\r\n"}, {"name": "list", "paramTypes": ["org.dromara.common.mybatis.core.page.PageQuery"], "doc": "\n 查询成就列表（分页）\r\n"}, {"name": "getActiveAchievements", "paramTypes": [], "doc": "\n 查询激活的成就列表\r\n"}, {"name": "getAchievementsByType", "paramTypes": ["java.lang.String"], "doc": "\n 根据成就类型查询成就列表\r\n"}, {"name": "initUserAchievements", "paramTypes": [], "doc": "\n 初始化用户成就进度\r\n"}, {"name": "checkUserAchievements", "paramTypes": [], "doc": "\n 手动检查用户成就\r\n"}], "constructors": []}