{"doc": " 题库Service接口\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "queryById", "paramTypes": ["java.lang.Long"], "doc": " 查询题库\n\n @param bankId 题库主键\n @return 题库\n"}, {"name": "queryList", "paramTypes": ["org.dromara.system.domain.bo.QuestionBankBo"], "doc": " 查询题库列表\n\n @param bo 题库查询条件\n @return 题库集合\n"}, {"name": "queryPageList", "paramTypes": ["org.dromara.system.domain.bo.QuestionBankBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 分页查询题库列表\n\n @param bo        题库查询条件\n @param pageQuery 分页参数\n @return 题库分页集合\n"}, {"name": "insertByBo", "paramTypes": ["org.dromara.system.domain.bo.QuestionBankBo"], "doc": " 新增题库\n\n @param bo 题库信息\n @return 新增结果\n"}, {"name": "updateByBo", "paramTypes": ["org.dromara.system.domain.bo.QuestionBankBo"], "doc": " 修改题库\n\n @param bo 题库信息\n @return 修改结果\n"}, {"name": "deleteWithValidByIds", "paramTypes": ["java.util.Collection"], "doc": " 校验并批量删除题库信息\n\n @param ids 需要删除的题库主键集合\n @return 删除结果\n"}, {"name": "queryByBankCode", "paramTypes": ["java.lang.String"], "doc": " 根据题库编码查询题库\n\n @param bankCode 题库编码\n @return 题库信息\n"}, {"name": "queryByMajorId", "paramTypes": ["java.lang.Long"], "doc": " 根据专业ID查询题库列表\n\n @param majorId 专业ID\n @return 题库集合\n"}, {"name": "queryBookmarkedBanks", "paramTypes": ["java.lang.Long"], "doc": " 查询用户收藏的题库列表\n\n @param userId 用户ID\n @return 题库集合\n"}, {"name": "queryHotBanks", "paramTypes": ["java.lang.Integer"], "doc": " 查询热门题库列表\n\n @param limit 限制数量\n @return 题库集合\n"}, {"name": "updatePracticeCount", "paramTypes": ["java.lang.Long"], "doc": " 更新题库练习次数\n\n @param bankId 题库ID\n @return 更新结果\n"}, {"name": "updateTotalQuestions", "paramTypes": ["java.lang.Long"], "doc": " 更新题库题目总数\n\n @param bankId 题库ID\n @return 更新结果\n"}, {"name": "checkBankCodeUnique", "paramTypes": ["org.dromara.system.domain.bo.QuestionBankBo"], "doc": " 检查题库编码是否唯一\n\n @param bo 题库信息\n @return 是否唯一\n"}, {"name": "importQuestionBank", "paramTypes": ["java.util.List"], "doc": " 批量导入题库\n\n @param list 题库列表\n @return 导入结果\n"}, {"name": "exportQuestionBank", "paramTypes": ["org.dromara.system.domain.bo.QuestionBankBo"], "doc": " 导出题库数据\n\n @param bo 题库查询条件\n @return 题库集合\n"}, {"name": "changeStatus", "paramTypes": ["java.lang.Long", "java.lang.String"], "doc": " 启用/停用题库\n\n @param bankId 题库ID\n @param status 状态\n @return 操作结果\n"}, {"name": "copyQuestionBank", "paramTypes": ["java.lang.Long", "java.lang.String"], "doc": " 复制题库\n\n @param bankId 源题库ID\n @param title  新题库标题\n @return 操作结果\n"}, {"name": "getStatistics", "paramTypes": ["java.lang.Long"], "doc": " 获取题库统计信息\n\n @param bankId 题库ID\n @return 统计信息\n"}], "constructors": []}