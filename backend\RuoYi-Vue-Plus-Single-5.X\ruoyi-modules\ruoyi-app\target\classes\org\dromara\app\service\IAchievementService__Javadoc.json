{"doc": "\n 成就系统服务接口\r\n\r\n <AUTHOR>\r\n @date 2025-07-18\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "queryPageList", "paramTypes": ["org.dromara.common.mybatis.core.page.PageQuery"], "doc": "\n 查询成就列表\r\n\r\n @param pageQuery 分页查询条件\r\n @return 成就列表\r\n"}, {"name": "queryActiveAchievements", "paramTypes": [], "doc": "\n 查询激活的成就列表\r\n\r\n @return 激活的成就列表\r\n"}, {"name": "queryByAchievementType", "paramTypes": ["java.lang.String"], "doc": "\n 根据成就类型查询成就列表\r\n\r\n @param achievementType 成就类型\r\n @return 成就列表\r\n"}, {"name": "queryByAchievementCode", "paramTypes": ["java.lang.String"], "doc": "\n 根据成就代码查询成就\r\n\r\n @param achievementCode 成就代码\r\n @return 成就信息\r\n"}, {"name": "checkAchievements", "paramTypes": ["java.lang.Long", "org.dromara.app.domain.dto.TrackEventDto"], "doc": "\n 检查用户成就\r\n\r\n @param userId        用户ID\r\n @param trackEventDto 用户行为事件\r\n"}, {"name": "getUserBadges", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.Bo<PERSON>an", "java.lang.String"], "doc": "\n 获取用户徽章列表\r\n\r\n @param userId   用户ID\r\n @param category 徽章类别（可选）\r\n @param unlocked 是否解锁（可选）\r\n @param rarity   稀有度（可选）\r\n @return 徽章列表\r\n"}, {"name": "getBadgeDetail", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": "\n 获取徽章详情\r\n\r\n @param userId  用户ID\r\n @param badgeId 徽章ID\r\n @return 徽章详情\r\n"}, {"name": "setPinStatus", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.Bo<PERSON>an"], "doc": "\n 设置徽章置顶状态\r\n\r\n @param userId   用户ID\r\n @param badgeId  徽章ID\r\n @param isPinned 是否置顶\r\n"}, {"name": "getPinnedBadges", "paramTypes": ["java.lang.String"], "doc": "\n 获取置顶徽章列表\r\n\r\n @param userId 用户ID\r\n @return 置顶徽章列表\r\n"}, {"name": "getAchievementStats", "paramTypes": ["java.lang.String"], "doc": "\n 获取成就统计信息\r\n\r\n @param userId 用户ID\r\n @return 成就统计信息\r\n"}, {"name": "getRecentAchievements", "paramTypes": ["java.lang.String", "java.lang.Integer"], "doc": "\n 获取最近解锁的成就\r\n\r\n @param userId 用户ID\r\n @param limit  数量限制\r\n @return 最近解锁的成就\r\n"}, {"name": "getCategories", "paramTypes": [], "doc": "\n 获取成就分类列表\r\n\r\n @return 成就分类列表（键为类别代码，值为类别名称）\r\n"}, {"name": "getInProgressAchievements", "paramTypes": ["java.lang.String"], "doc": "\n 获取进行中的成就\r\n\r\n @param userId 用户ID\r\n @return 进行中的成就\r\n"}, {"name": "getUserAchievementDetail", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": "\n 获取用户成就详情\r\n\r\n @param userId        用户ID\r\n @param achievementId 成就ID\r\n @return 用户成就详情\r\n"}, {"name": "checkAndUpdateAchievements", "paramTypes": ["java.lang.String"], "doc": "\n 检查并更新用户成就进度\r\n\r\n @param userId 用户ID\r\n @return 新解锁的成就列表\r\n"}, {"name": "shareAchievements", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": "\n 分享成就墙\r\n\r\n @param userId   用户ID\r\n @param platform 平台（wechat, qq, weibo, link）\r\n @return 分享结果\r\n"}, {"name": "recordEvent", "paramTypes": ["java.lang.String", "java.lang.String", "java.util.Map", "java.lang.Integer", "java.lang.String", "java.lang.String"], "doc": "\n 记录用户行为事件\r\n\r\n @param userId      用户ID\r\n @param eventType   事件类型\r\n @param eventData   事件数据\r\n @param eventValue  事件值\r\n @param relatedId   关联对象ID\r\n @param relatedType 关联对象类型\r\n @return 是否触发了新成就\r\n"}, {"name": "unlockAchievement", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.String"], "doc": "\n 解锁特定成就\r\n\r\n @param userId        用户ID\r\n @param achievementId 成就ID\r\n @param source        解锁来源\r\n @return 是否解锁成功\r\n"}, {"name": "getRecommendedAchievements", "paramTypes": ["java.lang.String", "java.lang.Integer"], "doc": "\n 获取推荐接下来完成的成就\r\n\r\n @param userId 用户ID\r\n @param limit  数量限制\r\n @return 推荐成就列表\r\n"}, {"name": "getLeaderboard", "paramTypes": ["java.lang.String", "java.lang.Integer"], "doc": "\n 获取成就排行榜\r\n\r\n @param category 类别（可选）\r\n @param limit    数量限制\r\n @return 排行榜列表\r\n"}, {"name": "getUserRankingInfo", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": "\n 获取用户在排行榜中的位置\r\n\r\n @param userId   用户ID\r\n @param category 类别（可选）\r\n @return 排行榜信息\r\n"}, {"name": "processUnhandledEvents", "paramTypes": ["int"], "doc": "\n 处理未处理的事件\r\n\r\n @param maxEvents 最大处理事件数\r\n @return 处理的事件数量\r\n"}, {"name": "updateAchievementProgress", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.Integer", "java.util.Map"], "doc": "\n 更新用户成就进度\r\n\r\n @param userId        用户ID\r\n @param achievementId 成就ID\r\n @param progress      进度值\r\n @param eventData     事件数据\r\n @return 是否触发了成就解锁\r\n"}, {"name": "initializeUserAchievements", "paramTypes": ["java.lang.String"], "doc": "\n 批量初始化用户成就\r\n\r\n @param userId 用户ID\r\n @return 初始化的成就数量\r\n"}, {"name": "getEventStatistics", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.Integer"], "doc": "\n 获取成就事件统计\r\n\r\n @param userId    用户ID\r\n @param eventType 事件类型（可选）\r\n @param days      统计天数\r\n @return 事件统计信息\r\n"}, {"name": "cleanupExpiredEvents", "paramTypes": ["int"], "doc": "\n 清理过期事件\r\n\r\n @param days 保留天数\r\n @return 清理的事件数量\r\n"}, {"name": "getUserAchievementCompletion", "paramTypes": ["java.lang.String"], "doc": "\n 获取用户成就完成度\r\n\r\n @param userId 用户ID\r\n @return 完成度信息\r\n"}, {"name": "recalculateUserProgress", "paramTypes": ["java.lang.String"], "doc": "\n 重新计算用户成就进度\r\n\r\n @param userId 用户ID\r\n @return 重新计算的成就数量\r\n"}, {"name": "getRecentUserAchievements", "paramTypes": ["java.lang.String", "java.lang.Integer"], "doc": "\n 获取最近解锁的成就\r\n\r\n @param userId 用户ID\r\n @param limit  数量限制\r\n @return 最近解锁的成就列表\r\n"}], "constructors": []}