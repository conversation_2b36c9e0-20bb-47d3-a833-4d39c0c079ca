package org.dromara.system.domain;

import io.github.linpeilie.AutoMapperConfig__138;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.bo.SysRoleBoToSysRoleMapper__67;
import org.dromara.system.domain.vo.SysRoleVo;
import org.dromara.system.domain.vo.SysRoleVoToSysRoleMapper__67;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__138.class,
    uses = {SysRoleVoToSysRoleMapper__67.class,SysRoleBoToSysRoleMapper__67.class},
    imports = {}
)
public interface SysRoleToSysRoleVoMapper__67 extends BaseMapper<SysRole, SysRoleVo> {
}
