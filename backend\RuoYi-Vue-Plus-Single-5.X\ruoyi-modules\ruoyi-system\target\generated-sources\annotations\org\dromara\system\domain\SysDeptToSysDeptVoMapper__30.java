package org.dromara.system.domain;

import io.github.linpeilie.AutoMapperConfig__138;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.bo.SysDeptBoToSysDeptMapper__30;
import org.dromara.system.domain.vo.SysDeptVo;
import org.dromara.system.domain.vo.SysDeptVoToSysDeptMapper__30;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__138.class,
    uses = {SysDeptBoToSysDeptMapper__30.class,SysDeptVoToSysDeptMapper__30.class,SysDeptBoToSysDeptMapper__30.class},
    imports = {}
)
public interface SysDeptToSysDeptVoMapper__30 extends BaseMapper<SysDept, SysDeptVo> {
}
