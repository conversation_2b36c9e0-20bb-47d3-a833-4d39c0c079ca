{"doc": " 题库管理\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["org.dromara.system.domain.bo.QuestionBankBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 查询题库列表\n"}, {"name": "export", "paramTypes": ["org.dromara.system.domain.bo.QuestionBankBo", "jakarta.servlet.http.HttpServletResponse"], "doc": " 导出题库列表\n"}, {"name": "getInfo", "paramTypes": ["java.lang.Long"], "doc": " 获取题库详细信息\n\n @param bankId 题库主键\n"}, {"name": "add", "paramTypes": ["org.dromara.system.domain.bo.QuestionBankBo"], "doc": " 新增题库\n"}, {"name": "edit", "paramTypes": ["org.dromara.system.domain.bo.QuestionBankBo"], "doc": " 修改题库\n"}, {"name": "remove", "paramTypes": ["java.lang.Long[]"], "doc": " 删除题库\n\n @param bankIds 题库主键串\n"}, {"name": "getByCode", "paramTypes": ["java.lang.String"], "doc": " 根据题库编码查询题库\n\n @param bankCode 题库编码\n"}, {"name": "getByMajorId", "paramTypes": ["java.lang.Long"], "doc": " 根据专业ID查询题库列表\n\n @param majorId 专业ID\n"}, {"name": "getBookmarkedBanks", "paramTypes": ["java.lang.Long"], "doc": " 查询用户收藏的题库列表\n\n @param userId 用户ID\n"}, {"name": "getHotBanks", "paramTypes": ["java.lang.Integer"], "doc": " 查询热门题库列表\n\n @param limit 限制数量\n"}, {"name": "updatePracticeCount", "paramTypes": ["java.lang.Long"], "doc": " 更新题库练习次数\n\n @param bankId 题库ID\n"}, {"name": "updateTotalQuestions", "paramTypes": ["java.lang.Long"], "doc": " 更新题库题目总数\n\n @param bankId 题库ID\n"}, {"name": "changeStatus", "paramTypes": ["java.lang.Long", "java.lang.String"], "doc": " 启用/停用题库\n\n @param bankId 题库ID\n @param status 状态\n"}, {"name": "copyQuestionBank", "paramTypes": ["java.lang.Long", "java.lang.String"], "doc": " 复制题库\n\n @param bankId 源题库ID\n @param title  新题库标题\n"}, {"name": "getStatistics", "paramTypes": ["java.lang.Long"], "doc": " 获取题库统计信息\n\n @param bankId 题库ID\n"}, {"name": "importData", "paramTypes": ["org.springframework.web.multipart.MultipartFile"], "doc": " 导入题库数据\n\n @param file 导入文件\n"}, {"name": "importTemplate", "paramTypes": ["jakarta.servlet.http.HttpServletResponse"], "doc": " 获取导入模板\n"}], "constructors": []}