package org.dromara.system.domain.vo;

import io.github.linpeilie.AutoMapperConfig__138;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.SysDictData;
import org.dromara.system.domain.SysDictDataToSysDictDataVoMapper__30;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__138.class,
    uses = {SysDictDataToSysDictDataVoMapper__30.class},
    imports = {}
)
public interface SysDictDataVoToSysDictDataMapper__30 extends BaseMapper<SysDictDataVo, SysDictData> {
}
