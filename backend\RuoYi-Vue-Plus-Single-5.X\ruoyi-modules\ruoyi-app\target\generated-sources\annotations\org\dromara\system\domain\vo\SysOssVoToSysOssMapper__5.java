package org.dromara.system.domain.vo;

import io.github.linpeilie.AutoMapperConfig__139;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.SysOss;
import org.dromara.system.domain.SysOssToSysOssVoMapper__5;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__139.class,
    uses = {SysOssToSysOssVoMapper__5.class},
    imports = {}
)
public interface SysOssVoToSysOssMapper__5 extends BaseMapper<SysOssVo, SysOss> {
}
