package org.dromara.system.domain.vo;

import io.github.linpeilie.AutoMapperConfig__138;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.SysOperLog;
import org.dromara.system.domain.SysOperLogToSysOperLogVoMapper__30;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__138.class,
    uses = {SysOperLogToSysOperLogVoMapper__30.class},
    imports = {}
)
public interface SysOperLogVoToSysOperLogMapper__30 extends BaseMapper<SysOperLogVo, SysOperLog> {
}
