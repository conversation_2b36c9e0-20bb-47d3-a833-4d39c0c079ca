package org.dromara.app.domain.vo;

import io.github.linpeilie.AutoMapperConfig__139;
import io.github.linpeilie.BaseMapper;
import org.dromara.app.domain.UserResume;
import org.dromara.app.domain.UserResumeToUserResumeVoMapper__5;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__139.class,
    uses = {UserResumeToUserResumeVoMapper__5.class},
    imports = {}
)
public interface UserResumeVoToUserResumeMapper__5 extends BaseMapper<UserResumeVo, UserResume> {
}
