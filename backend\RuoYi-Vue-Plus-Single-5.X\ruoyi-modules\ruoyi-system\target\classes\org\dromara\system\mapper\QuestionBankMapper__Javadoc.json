{"doc": " 题库Mapper接口\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectVoById", "paramTypes": ["java.lang.Long"], "doc": " 查询题库\n\n @param bankId 题库主键\n @return 题库\n"}, {"name": "selectVoList", "paramTypes": ["org.dromara.system.domain.bo.QuestionBankBo"], "doc": " 查询题库列表\n\n @param questionBank 题库\n @return 题库集合\n"}, {"name": "selectVoPage", "paramTypes": ["com.baomidou.mybatisplus.extension.plugins.pagination.Page", "org.dromara.system.domain.bo.QuestionBankBo"], "doc": " 分页查询题库列表\n\n @param page         分页参数\n @param questionBank 题库查询条件\n @return 题库集合\n"}, {"name": "selectPageQuestionBankList", "paramTypes": ["org.dromara.common.mybatis.core.page.PageQuery", "org.dromara.system.domain.bo.QuestionBankBo"], "doc": " 根据条件分页查询题库列表\n\n @param pageQuery    分页参数\n @param questionBank 题库查询条件\n @return 题库集合\n"}, {"name": "selectVoByIds", "paramTypes": ["java.util.Collection"], "doc": " 查询题库列表\n\n @param bankIds 题库主键集合\n @return 题库集合\n"}, {"name": "selectVoByBankCode", "paramTypes": ["java.lang.String"], "doc": " 根据题库编码查询题库\n\n @param bankCode 题库编码\n @return 题库\n"}, {"name": "selectVoByMajorId", "paramTypes": ["java.lang.Long"], "doc": " 根据专业ID查询题库列表\n\n @param majorId 专业ID\n @return 题库集合\n"}, {"name": "selectBookmarkedBanksByUserId", "paramTypes": ["java.lang.Long"], "doc": " 查询用户收藏的题库列表\n\n @param userId 用户ID\n @return 题库集合\n"}, {"name": "selectHotQuestionBanks", "paramTypes": ["java.lang.Integer"], "doc": " 查询热门题库列表\n\n @param limit 限制数量\n @return 题库集合\n"}, {"name": "updatePracticeCount", "paramTypes": ["java.lang.Long"], "doc": " 更新题库练习次数\n\n @param bankId 题库ID\n @return 影响行数\n"}, {"name": "updateTotalQuestions", "paramTypes": ["java.lang.Long"], "doc": " 更新题库题目总数\n\n @param bankId 题库ID\n @return 影响行数\n"}, {"name": "deleteQuestionBankByIds", "paramTypes": ["java.util.Collection"], "doc": " 批量删除题库\n\n @param bankIds 需要删除的题库主键集合\n @return 影响行数\n"}, {"name": "checkBankCodeUnique", "paramTypes": ["java.lang.String", "java.lang.Long"], "doc": " 检查题库编码是否唯一\n\n @param bankCode 题库编码\n @param bankId   题库ID（编辑时排除自己）\n @return 数量\n"}], "constructors": []}