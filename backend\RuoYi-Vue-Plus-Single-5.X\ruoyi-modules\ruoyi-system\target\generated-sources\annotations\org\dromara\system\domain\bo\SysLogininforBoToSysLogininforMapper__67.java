package org.dromara.system.domain.bo;

import io.github.linpeilie.AutoMapperConfig__138;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.SysLogininfor;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__138.class,
    uses = {},
    imports = {}
)
public interface SysLogininforBoToSysLogininforMapper__67 extends BaseMapper<SysLogininforBo, SysLogininfor> {
}
