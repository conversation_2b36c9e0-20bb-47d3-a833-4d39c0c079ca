package org.dromara.common.mybatis.core.domain;

import io.github.linpeilie.AutoMapperConfig__138;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.bo.QuestionBoToQuestionMapper__64;
import org.dromara.system.domain.vo.QuestionVo;
import org.dromara.system.domain.vo.QuestionVoToQuestionMapper__62;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__138.class,
    uses = {QuestionBoToQuestionMapper__64.class,QuestionVoToQuestionMapper__62.class},
    imports = {}
)
public interface QuestionToQuestionVoMapper__62 extends BaseMapper<Question, QuestionVo> {
}
