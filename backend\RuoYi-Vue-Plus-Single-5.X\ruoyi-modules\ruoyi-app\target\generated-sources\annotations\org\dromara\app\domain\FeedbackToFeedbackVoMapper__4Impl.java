package org.dromara.app.domain;

import javax.annotation.processing.Generated;
import org.dromara.app.domain.vo.FeedbackVo;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-01T21:52:42+0800",
    comments = "version: 1.5.5.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250729-0351, environment: Java 21.0.8 (Eclipse Adoptium)"
)
@Component
public class FeedbackToFeedbackVoMapper__4Impl implements FeedbackToFeedbackVoMapper__4 {

    @Override
    public FeedbackVo convert(Feedback arg0) {
        if ( arg0 == null ) {
            return null;
        }

        FeedbackVo feedbackVo = new FeedbackVo();

        feedbackVo.setAppVersion( arg0.getAppVersion() );
        feedbackVo.setContactInfo( arg0.getContactInfo() );
        feedbackVo.setContent( arg0.getContent() );
        feedbackVo.setCreateTime( arg0.getCreateTime() );
        feedbackVo.setDeviceInfo( arg0.getDeviceInfo() );
        feedbackVo.setHandler( arg0.getHandler() );
        feedbackVo.setId( arg0.getId() );
        feedbackVo.setPlatform( arg0.getPlatform() );
        feedbackVo.setReply( arg0.getReply() );
        feedbackVo.setStatus( arg0.getStatus() );
        feedbackVo.setType( arg0.getType() );
        feedbackVo.setUpdateTime( arg0.getUpdateTime() );
        feedbackVo.setUserId( arg0.getUserId() );

        return feedbackVo;
    }

    @Override
    public FeedbackVo convert(Feedback arg0, FeedbackVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setAppVersion( arg0.getAppVersion() );
        arg1.setContactInfo( arg0.getContactInfo() );
        arg1.setContent( arg0.getContent() );
        arg1.setCreateTime( arg0.getCreateTime() );
        arg1.setDeviceInfo( arg0.getDeviceInfo() );
        arg1.setHandler( arg0.getHandler() );
        arg1.setId( arg0.getId() );
        arg1.setPlatform( arg0.getPlatform() );
        arg1.setReply( arg0.getReply() );
        arg1.setStatus( arg0.getStatus() );
        arg1.setType( arg0.getType() );
        arg1.setUpdateTime( arg0.getUpdateTime() );
        arg1.setUserId( arg0.getUserId() );

        return arg1;
    }
}
