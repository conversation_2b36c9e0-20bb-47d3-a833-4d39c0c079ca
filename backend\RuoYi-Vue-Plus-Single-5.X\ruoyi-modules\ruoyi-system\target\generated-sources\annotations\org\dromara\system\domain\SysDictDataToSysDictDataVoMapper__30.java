package org.dromara.system.domain;

import io.github.linpeilie.AutoMapperConfig__138;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.bo.SysDictDataBoToSysDictDataMapper__30;
import org.dromara.system.domain.vo.SysDictDataVo;
import org.dromara.system.domain.vo.SysDictDataVoToSysDictDataMapper__30;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__138.class,
    uses = {SysDictDataBoToSysDictDataMapper__30.class,SysDictDataVoToSysDictDataMapper__30.class},
    imports = {}
)
public interface SysDictDataToSysDictDataVoMapper__30 extends BaseMapper<SysDictData, SysDictDataVo> {
}
