package org.dromara.system.domain;

import io.github.linpeilie.AutoMapperConfig__139;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.bo.SysRoleBoToSysRoleMapper__5;
import org.dromara.system.domain.vo.SysRoleVo;
import org.dromara.system.domain.vo.SysRoleVoToSysRoleMapper__5;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__139.class,
    uses = {SysRoleVoToSysRoleMapper__5.class,SysRoleBoToSysRoleMapper__5.class},
    imports = {}
)
public interface SysRoleToSysRoleVoMapper__5 extends BaseMapper<SysRole, SysRoleVo> {
}
