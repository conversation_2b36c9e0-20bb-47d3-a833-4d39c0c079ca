package org.dromara.system.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.system.domain.bo.QuestionCommentBo;
import org.dromara.system.domain.vo.QuestionCommentVo;
import org.dromara.system.mapper.QuestionCommentMapper;
import org.dromara.system.service.IQuestionCommentService;
import org.dromara.app.domain.QuestionComment;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 题目评论Service业务层处理
 *
 * <AUTHOR>
 */
@RequiredArgsConstructor
@Service
public class QuestionCommentServiceImpl implements IQuestionCommentService {

    private final QuestionCommentMapper baseMapper;

    /**
     * 查询题目评论
     */
    @Override
    public QuestionCommentVo queryById(Long commentId) {
        return baseMapper.selectVoById(commentId);
    }

    /**
     * 查询题目评论列表
     */
    @Override
    public List<QuestionCommentVo> queryList(QuestionCommentBo bo) {
        LambdaQueryWrapper<QuestionComment> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(bo);
    }

    /**
     * 分页查询题目评论列表
     */
    @Override
    public TableDataInfo<QuestionCommentVo> queryPageList(QuestionCommentBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<QuestionComment> lqw = buildQueryWrapper(bo);
        Page<QuestionCommentVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 根据题目ID分页查询评论列表
     */
    @Override
    public TableDataInfo<QuestionCommentVo> queryPageListByQuestionId(Long questionId, QuestionCommentBo bo, PageQuery pageQuery) {
        Page<QuestionCommentVo> result = baseMapper.selectPageCommentByQuestionId(pageQuery, questionId, bo);
        return TableDataInfo.build(result);
    }

    /**
     * 构建查询条件
     */
    private LambdaQueryWrapper<QuestionComment> buildQueryWrapper(QuestionCommentBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<QuestionComment> lqw = Wrappers.lambdaQueryWrapper();
        lqw.eq(ObjectUtil.isNotNull(bo.getQuestionId()), QuestionComment::getQuestionId, bo.getQuestionId());
        lqw.eq(ObjectUtil.isNotNull(bo.getUserId()), QuestionComment::getUserId, bo.getUserId());
        lqw.eq(ObjectUtil.isNotNull(bo.getParentId()), QuestionComment::getParentId, bo.getParentId());
        lqw.like(StringUtils.isNotBlank(bo.getContent()), QuestionComment::getContent, bo.getContent());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), QuestionComment::getStatus, bo.getStatus());
        lqw.orderByAsc(QuestionComment::getSort).orderByDesc(QuestionComment::getCreateTime);
        return lqw;
    }

    /**
     * 新增题目评论
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertByBo(QuestionCommentBo bo) {
        QuestionComment add = MapstructUtils.convert(bo, QuestionComment.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setCommentId(add.getCommentId());
            // 如果是回复评论，更新父评论的回复数
            if (ObjectUtil.isNotNull(bo.getParentId())) {
                updateReplyCount(bo.getParentId(), 1);
            }
        }
        return flag;
    }

    /**
     * 修改题目评论
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateByBo(QuestionCommentBo bo) {
        QuestionComment update = MapstructUtils.convert(bo, QuestionComment.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(QuestionComment entity) {
        // 可以在此处添加业务校验逻辑
    }

    /**
     * 批量删除题目评论
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteWithValidByIds(Collection<Long> ids) {
        return baseMapper.deleteQuestionCommentByIds(ids) > 0;
    }

    /**
     * 根据题目ID查询评论列表
     */
    @Override
    public List<QuestionCommentVo> queryByQuestionId(Long questionId) {
        return baseMapper.selectVoByQuestionId(questionId);
    }

    /**
     * 根据用户ID查询评论列表
     */
    @Override
    public List<QuestionCommentVo> queryByUserId(Long userId) {
        return baseMapper.selectVoByUserId(userId);
    }

    /**
     * 根据父评论ID查询子评论列表
     */
    @Override
    public List<QuestionCommentVo> queryByParentId(Long parentId) {
        return baseMapper.selectVoByParentId(parentId);
    }

    /**
     * 查询评论树形结构
     */
    @Override
    public List<QuestionCommentVo> queryCommentTree(Long questionId) {
        return baseMapper.selectCommentTree(questionId);
    }

    /**
     * 查询热门评论列表
     */
    @Override
    public List<QuestionCommentVo> queryHotComments(Long questionId, Integer limit) {
        return baseMapper.selectHotComments(questionId, limit);
    }

    /**
     * 查询最新评论列表
     */
    @Override
    public List<QuestionCommentVo> queryLatestComments(Long questionId, Integer limit) {
        return baseMapper.selectLatestComments(questionId, limit);
    }

    /**
     * 更新评论点赞数
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateLikeCount(Long commentId, Integer increment) {
        return baseMapper.updateLikeCount(commentId, increment) > 0;
    }

    /**
     * 更新评论回复数
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateReplyCount(Long commentId, Integer increment) {
        return baseMapper.updateReplyCount(commentId, increment) > 0;
    }

    /**
     * 点赞/取消点赞评论
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean likeComment(Long commentId, Long userId) {
        // 这里需要实现点赞逻辑，包括检查是否已点赞、更新点赞状态等
        // 简化实现，直接增加点赞数
        return updateLikeCount(commentId, 1);
    }

    /**
     * 回复评论
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean replyComment(QuestionCommentBo bo) {
        return insertByBo(bo);
    }

    /**
     * 根据题目ID删除评论
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteByQuestionId(Long questionId) {
        return baseMapper.deleteCommentByQuestionId(questionId) > 0;
    }

    /**
     * 根据用户ID删除评论
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteByUserId(Long userId) {
        return baseMapper.deleteCommentByUserId(userId) > 0;
    }

    /**
     * 统计题目下的评论数量
     */
    @Override
    public Integer countByQuestionId(Long questionId) {
        return baseMapper.countCommentsByQuestionId(questionId);
    }

    /**
     * 统计用户的评论数量
     */
    @Override
    public Integer countByUserId(Long userId) {
        return baseMapper.countCommentsByUserId(userId);
    }

    /**
     * 审核评论
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean auditComment(Long commentId, String status) {
        QuestionComment comment = new QuestionComment();
        comment.setCommentId(commentId);
        comment.setStatus(status);
        return baseMapper.updateById(comment) > 0;
    }

    /**
     * 批量审核评论
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean batchAuditComments(Collection<Long> commentIds, String status) {
        if (CollUtil.isEmpty(commentIds)) {
            return false;
        }
        for (Long commentId : commentIds) {
            auditComment(commentId, status);
        }
        return true;
    }

    /**
     * 导出评论数据
     */
    @Override
    public List<QuestionCommentVo> exportComment(QuestionCommentBo bo) {
        return queryList(bo);
    }
}
