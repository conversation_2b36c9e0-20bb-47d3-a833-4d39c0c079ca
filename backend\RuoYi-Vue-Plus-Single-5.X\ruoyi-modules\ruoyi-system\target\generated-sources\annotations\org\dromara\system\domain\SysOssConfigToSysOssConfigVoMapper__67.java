package org.dromara.system.domain;

import io.github.linpeilie.AutoMapperConfig__138;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.bo.SysOssConfigBoToSysOssConfigMapper__67;
import org.dromara.system.domain.vo.SysOssConfigVo;
import org.dromara.system.domain.vo.SysOssConfigVoToSysOssConfigMapper__67;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__138.class,
    uses = {SysOssConfigVoToSysOssConfigMapper__67.class,SysOssConfigBoToSysOssConfigMapper__67.class},
    imports = {}
)
public interface SysOssConfigToSysOssConfigVoMapper__67 extends BaseMapper<SysOssConfig, SysOssConfigVo> {
}
