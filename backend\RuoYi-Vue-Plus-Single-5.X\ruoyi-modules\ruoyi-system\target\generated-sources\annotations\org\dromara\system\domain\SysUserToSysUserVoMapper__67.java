package org.dromara.system.domain;

import io.github.linpeilie.AutoMapperConfig__138;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.bo.SysUserBoToSysUserMapper__67;
import org.dromara.system.domain.vo.SysRoleVoToSysRoleMapper__67;
import org.dromara.system.domain.vo.SysUserVo;
import org.dromara.system.domain.vo.SysUserVoToSysUserMapper__67;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__138.class,
    uses = {SysRoleVoToSysRoleMapper__67.class,SysRoleToSysRoleVoMapper__67.class,SysUserVoToSysUserMapper__67.class,SysUserBoToSysUserMapper__67.class},
    imports = {}
)
public interface SysUserToSysUserVoMapper__67 extends BaseMapper<SysUser, SysUserVo> {
}
