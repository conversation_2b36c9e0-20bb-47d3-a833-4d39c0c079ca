package org.dromara.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.dromara.common.mybatis.annotation.DataPermission;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.system.domain.bo.QuestionBo;
import org.dromara.system.domain.vo.QuestionVo;
import org.dromara.common.mybatis.core.domain.Question;

import java.util.Collection;
import java.util.List;

/**
 * 题目Mapper接口
 *
 * <AUTHOR>
 */
public interface QuestionMapper extends BaseMapper<Question> {

    /**
     * 查询题目
     *
     * @param questionId 题目主键
     * @return 题目
     */
    QuestionVo selectVoById(Long questionId);

    /**
     * 查询题目列表
     *
     * @param question 题目
     * @return 题目集合
     */
    @DataPermission(key = "questionId")
    List<QuestionVo> selectVoList(QuestionBo question);

    /**
     * 分页查询题目列表
     *
     * @param page     分页参数
     * @param question 题目查询条件
     * @return 题目集合
     */
    @DataPermission(key = "questionId")
    Page<QuestionVo> selectVoPage(Page<Question> page, @Param("ew") QuestionBo question);

    /**
     * 根据条件分页查询题目列表
     *
     * @param pageQuery 分页参数
     * @param question  题目查询条件
     * @return 题目集合
     */
    @DataPermission(key = "questionId")
    Page<QuestionVo> selectPageQuestionList(@Param("page") PageQuery pageQuery, @Param("question") QuestionBo question);

    /**
     * 查询题目列表
     *
     * @param questionIds 题目主键集合
     * @return 题目集合
     */
    List<QuestionVo> selectVoByIds(@Param("questionIds") Collection<Long> questionIds);

    /**
     * 根据题库ID查询题目列表
     *
     * @param bankId 题库ID
     * @return 题目集合
     */
    List<QuestionVo> selectVoByBankId(@Param("bankId") Long bankId);

    /**
     * 根据题库ID分页查询题目列表
     *
     * @param pageQuery 分页参数
     * @param bankId    题库ID
     * @param question  题目查询条件
     * @return 题目集合
     */
    Page<QuestionVo> selectPageQuestionByBankId(@Param("page") PageQuery pageQuery, @Param("bankId") Long bankId, @Param("question") QuestionBo question);

    /**
     * 根据题目编码查询题目
     *
     * @param questionCode 题目编码
     * @return 题目
     */
    QuestionVo selectVoByQuestionCode(@Param("questionCode") String questionCode);

    /**
     * 根据分类查询题目列表
     *
     * @param category 分类
     * @return 题目集合
     */
    List<QuestionVo> selectVoByCategory(@Param("category") String category);

    /**
     * 根据难度查询题目列表
     *
     * @param difficulty 难度
     * @return 题目集合
     */
    List<QuestionVo> selectVoByDifficulty(@Param("difficulty") Integer difficulty);

    /**
     * 根据题目类型查询题目列表
     *
     * @param type 题目类型
     * @return 题目集合
     */
    List<QuestionVo> selectVoByType(@Param("type") Integer type);

    /**
     * 查询用户收藏的题目列表
     *
     * @param userId 用户ID
     * @return 题目集合
     */
    List<QuestionVo> selectBookmarkedQuestionsByUserId(@Param("userId") Long userId);

    /**
     * 查询热门题目列表
     *
     * @param limit 限制数量
     * @return 题目集合
     */
    List<QuestionVo> selectHotQuestions(@Param("limit") Integer limit);

    /**
     * 随机查询题目列表
     *
     * @param bankId 题库ID
     * @param limit  限制数量
     * @return 题目集合
     */
    List<QuestionVo> selectRandomQuestions(@Param("bankId") Long bankId, @Param("limit") Integer limit);

    /**
     * 更新题目练习次数
     *
     * @param questionId 题目ID
     * @return 影响行数
     */
    int updatePracticeCount(@Param("questionId") Long questionId);

    /**
     * 更新题目评论数
     *
     * @param questionId 题目ID
     * @return 影响行数
     */
    int updateCommentCount(@Param("questionId") Long questionId);

    /**
     * 更新题目正确率
     *
     * @param questionId  题目ID
     * @param correctRate 正确率
     * @return 影响行数
     */
    int updateCorrectRate(@Param("questionId") Long questionId, @Param("correctRate") Integer correctRate);

    /**
     * 批量删除题目
     *
     * @param questionIds 需要删除的题目主键集合
     * @return 影响行数
     */
    int deleteQuestionByIds(@Param("questionIds") Collection<Long> questionIds);

    /**
     * 根据题库ID删除题目
     *
     * @param bankId 题库ID
     * @return 影响行数
     */
    int deleteQuestionByBankId(@Param("bankId") Long bankId);

    /**
     * 检查题目编码是否唯一
     *
     * @param questionCode 题目编码
     * @param questionId   题目ID（编辑时排除自己）
     * @return 数量
     */
    int checkQuestionCodeUnique(@Param("questionCode") String questionCode, @Param("questionId") Long questionId);

    /**
     * 统计题库下的题目数量
     *
     * @param bankId 题库ID
     * @return 题目数量
     */
    int countQuestionsByBankId(@Param("bankId") Long bankId);
}
