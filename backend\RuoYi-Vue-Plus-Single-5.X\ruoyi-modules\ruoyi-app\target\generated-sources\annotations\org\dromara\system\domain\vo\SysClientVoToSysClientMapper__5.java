package org.dromara.system.domain.vo;

import io.github.linpeilie.AutoMapperConfig__139;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.SysClient;
import org.dromara.system.domain.SysClientToSysClientVoMapper__5;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__139.class,
    uses = {SysClientToSysClientVoMapper__5.class},
    imports = {}
)
public interface SysClientVoToSysClientMapper__5 extends BaseMapper<SysClientVo, SysClient> {
}
