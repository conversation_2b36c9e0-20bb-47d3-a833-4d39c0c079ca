package org.dromara.system.domain;

import io.github.linpeilie.AutoMapperConfig__138;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.bo.SysPostBoToSysPostMapper__67;
import org.dromara.system.domain.vo.SysPostVo;
import org.dromara.system.domain.vo.SysPostVoToSysPostMapper__67;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__138.class,
    uses = {SysPostBoToSysPostMapper__67.class,SysPostVoToSysPostMapper__67.class},
    imports = {}
)
public interface SysPostToSysPostVoMapper__67 extends BaseMapper<SysPost, SysPostVo> {
}
