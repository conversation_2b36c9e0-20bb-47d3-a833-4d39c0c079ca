package org.dromara.system.domain.vo;

import io.github.linpeilie.AutoMapperConfig__138;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.SysOss;
import org.dromara.system.domain.SysOssToSysOssVoMapper__30;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__138.class,
    uses = {SysOssToSysOssVoMapper__30.class},
    imports = {}
)
public interface SysOssVoToSysOssMapper__30 extends BaseMapper<SysOssVo, SysOss> {
}
