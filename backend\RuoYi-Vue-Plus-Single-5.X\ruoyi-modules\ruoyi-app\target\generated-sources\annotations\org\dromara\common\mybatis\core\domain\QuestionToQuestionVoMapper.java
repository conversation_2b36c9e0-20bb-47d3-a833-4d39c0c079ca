package org.dromara.common.mybatis.core.domain;

import io.github.linpeilie.AutoMapperConfig__139;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.bo.QuestionBoToQuestionMapper__1;
import org.dromara.system.domain.vo.QuestionVo;
import org.dromara.system.domain.vo.QuestionVoToQuestionMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__139.class,
    uses = {QuestionBoToQuestionMapper__1.class,QuestionVoToQuestionMapper.class},
    imports = {}
)
public interface QuestionToQuestionVoMapper extends BaseMapper<Question, QuestionVo> {
}
