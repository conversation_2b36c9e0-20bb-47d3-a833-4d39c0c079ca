package org.dromara.system.domain;

import io.github.linpeilie.AutoMapperConfig__138;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.bo.SysOperLogBoToSysOperLogMapper__30;
import org.dromara.system.domain.vo.SysOperLogVo;
import org.dromara.system.domain.vo.SysOperLogVoToSysOperLogMapper__30;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__138.class,
    uses = {SysOperLogBoToSysOperLogMapper__30.class,SysOperLogVoToSysOperLogMapper__30.class},
    imports = {}
)
public interface SysOperLogToSysOperLogVoMapper__30 extends BaseMapper<SysOperLog, SysOperLogVo> {
}
