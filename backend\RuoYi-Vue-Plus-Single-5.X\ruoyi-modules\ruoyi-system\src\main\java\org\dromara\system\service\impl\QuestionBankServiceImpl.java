package org.dromara.system.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.system.domain.bo.QuestionBankBo;
import org.dromara.system.domain.vo.QuestionBankVo;
import org.dromara.system.mapper.QuestionBankMapper;
import org.dromara.system.service.IQuestionBankService;
import org.dromara.app.domain.QuestionBank;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 题库Service业务层处理
 *
 * <AUTHOR>
 */
@RequiredArgsConstructor
@Service
public class QuestionBankServiceImpl implements IQuestionBankService {

    private final QuestionBankMapper baseMapper;

    /**
     * 查询题库
     */
    @Override
    public QuestionBankVo queryById(Long bankId) {
        return baseMapper.selectVoById(bankId);
    }

    /**
     * 查询题库列表
     */
    @Override
    public List<QuestionBankVo> queryList(QuestionBankBo bo) {
        LambdaQueryWrapper<QuestionBank> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(bo);
    }

    /**
     * 分页查询题库列表
     */
    @Override
    public TableDataInfo<QuestionBankVo> queryPageList(QuestionBankBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<QuestionBank> lqw = buildQueryWrapper(bo);
        Page<QuestionBankVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 构建查询条件
     */
    private LambdaQueryWrapper<QuestionBank> buildQueryWrapper(QuestionBankBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<QuestionBank> lqw = Wrappers.lambdaQueryWrapper();
        lqw.like(StringUtils.isNotBlank(bo.getBankCode()), QuestionBank::getBankCode, bo.getBankCode());
        lqw.like(StringUtils.isNotBlank(bo.getTitle()), QuestionBank::getTitle, bo.getTitle());
        lqw.eq(ObjectUtil.isNotNull(bo.getMajorId()), QuestionBank::getMajorId, bo.getMajorId());
        lqw.eq(ObjectUtil.isNotNull(bo.getDifficulty()), QuestionBank::getDifficulty, bo.getDifficulty());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), QuestionBank::getStatus, bo.getStatus());
        lqw.orderByAsc(QuestionBank::getSort).orderByDesc(QuestionBank::getCreateTime);
        return lqw;
    }

    /**
     * 新增题库
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertByBo(QuestionBankBo bo) {
        QuestionBank add = MapstructUtils.convert(bo, QuestionBank.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setBankId(add.getBankId());
        }
        return flag;
    }

    /**
     * 修改题库
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateByBo(QuestionBankBo bo) {
        QuestionBank update = MapstructUtils.convert(bo, QuestionBank.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(QuestionBank entity) {
        // 可以在此处添加业务校验逻辑
    }

    /**
     * 批量删除题库
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteWithValidByIds(Collection<Long> ids) {
        return baseMapper.deleteQuestionBankByIds(ids) > 0;
    }

    /**
     * 根据题库编码查询题库
     */
    @Override
    public QuestionBankVo queryByBankCode(String bankCode) {
        return baseMapper.selectVoByBankCode(bankCode);
    }

    /**
     * 根据专业ID查询题库列表
     */
    @Override
    public List<QuestionBankVo> queryByMajorId(Long majorId) {
        return baseMapper.selectVoByMajorId(majorId);
    }

    /**
     * 查询用户收藏的题库列表
     */
    @Override
    public List<QuestionBankVo> queryBookmarkedBanks(Long userId) {
        return baseMapper.selectBookmarkedBanksByUserId(userId);
    }

    /**
     * 查询热门题库列表
     */
    @Override
    public List<QuestionBankVo> queryHotBanks(Integer limit) {
        return baseMapper.selectHotQuestionBanks(limit);
    }

    /**
     * 更新题库练习次数
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updatePracticeCount(Long bankId) {
        return baseMapper.updatePracticeCount(bankId) > 0;
    }

    /**
     * 更新题库题目总数
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateTotalQuestions(Long bankId) {
        return baseMapper.updateTotalQuestions(bankId) > 0;
    }

    /**
     * 检查题库编码是否唯一
     */
    @Override
    public Boolean checkBankCodeUnique(QuestionBankBo bo) {
        Long bankId = ObjectUtil.isNull(bo.getBankId()) ? -1L : bo.getBankId();
        QuestionBank questionBank = baseMapper.selectOne(Wrappers.lambdaQuery(QuestionBank.class)
            .eq(QuestionBank::getBankCode, bo.getBankCode())
            .ne(QuestionBank::getBankId, bankId));
        return ObjectUtil.isNull(questionBank);
    }

    /**
     * 批量导入题库
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String importQuestionBank(List<QuestionBankBo> list) {
        if (CollUtil.isEmpty(list)) {
            return "导入数据不能为空";
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        for (QuestionBankBo bo : list) {
            try {
                // 验证是否存在这个题库
                if (!checkBankCodeUnique(bo)) {
                    failureNum++;
                    failureMsg.append("<br/>").append(failureNum).append("、题库编码 ").append(bo.getBankCode()).append(" 已存在");
                } else {
                    insertByBo(bo);
                    successNum++;
                    successMsg.append("<br/>").append(successNum).append("、题库 ").append(bo.getTitle()).append(" 导入成功");
                }
            } catch (Exception e) {
                failureNum++;
                String msg = "<br/>" + failureNum + "、题库 " + bo.getTitle() + " 导入失败：";
                failureMsg.append(msg).append(e.getMessage());
            }
        }
        if (failureNum > 0) {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            throw new RuntimeException(failureMsg.toString());
        } else {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
        }
        return successMsg.toString();
    }

    /**
     * 导出题库数据
     */
    @Override
    public List<QuestionBankVo> exportQuestionBank(QuestionBankBo bo) {
        return queryList(bo);
    }

    /**
     * 启用/停用题库
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean changeStatus(Long bankId, String status) {
        QuestionBank questionBank = new QuestionBank();
        questionBank.setBankId(bankId);
        questionBank.setStatus(status);
        return baseMapper.updateById(questionBank) > 0;
    }

    /**
     * 复制题库
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean copyQuestionBank(Long bankId, String title) {
        QuestionBank original = baseMapper.selectById(bankId);
        if (ObjectUtil.isNull(original)) {
            return false;
        }
        QuestionBank copy = BeanUtil.copyProperties(original, QuestionBank.class);
        copy.setBankId(null);
        copy.setTitle(title);
        copy.setBankCode(original.getBankCode() + "_copy");
        copy.setTotalQuestions(0);
        copy.setPracticeCount(0);
        return baseMapper.insert(copy) > 0;
    }

    /**
     * 获取题库统计信息
     */
    @Override
    public QuestionBankVo getStatistics(Long bankId) {
        QuestionBankVo vo = queryById(bankId);
        if (ObjectUtil.isNotNull(vo)) {
            // 这里可以添加更多统计信息的计算
        }
        return vo;
    }
}
