package org.dromara.system.domain;

import io.github.linpeilie.AutoMapperConfig__138;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.bo.SysDictTypeBoToSysDictTypeMapper__30;
import org.dromara.system.domain.vo.SysDictTypeVo;
import org.dromara.system.domain.vo.SysDictTypeVoToSysDictTypeMapper__30;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__138.class,
    uses = {SysDictTypeBoToSysDictTypeMapper__30.class,SysDictTypeVoToSysDictTypeMapper__30.class},
    imports = {}
)
public interface SysDictTypeToSysDictTypeVoMapper__30 extends BaseMapper<SysDictType, SysDictTypeVo> {
}
