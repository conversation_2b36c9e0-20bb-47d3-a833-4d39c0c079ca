package org.dromara.system.domain;

import io.github.linpeilie.AutoMapperConfig__138;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.bo.SysUserBoToSysUserMapper__30;
import org.dromara.system.domain.vo.SysRoleVoToSysRoleMapper__30;
import org.dromara.system.domain.vo.SysUserVo;
import org.dromara.system.domain.vo.SysUserVoToSysUserMapper__30;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__138.class,
    uses = {SysRoleVoToSysRoleMapper__30.class,SysRoleToSysRoleVoMapper__30.class,SysUserVoToSysUserMapper__30.class,SysUserBoToSysUserMapper__30.class},
    imports = {}
)
public interface SysUserToSysUserVoMapper__30 extends BaseMapper<SysUser, SysUserVo> {
}
