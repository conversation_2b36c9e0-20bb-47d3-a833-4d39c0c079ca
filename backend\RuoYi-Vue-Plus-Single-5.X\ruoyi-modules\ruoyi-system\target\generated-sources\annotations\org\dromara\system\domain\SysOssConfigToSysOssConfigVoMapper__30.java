package org.dromara.system.domain;

import io.github.linpeilie.AutoMapperConfig__138;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.bo.SysOssConfigBoToSysOssConfigMapper__30;
import org.dromara.system.domain.vo.SysOssConfigVo;
import org.dromara.system.domain.vo.SysOssConfigVoToSysOssConfigMapper__30;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__138.class,
    uses = {SysOssConfigVoToSysOssConfigMapper__30.class,SysOssConfigBoToSysOssConfigMapper__30.class},
    imports = {}
)
public interface SysOssConfigToSysOssConfigVoMapper__30 extends BaseMapper<SysOssConfig, SysOssConfigVo> {
}
