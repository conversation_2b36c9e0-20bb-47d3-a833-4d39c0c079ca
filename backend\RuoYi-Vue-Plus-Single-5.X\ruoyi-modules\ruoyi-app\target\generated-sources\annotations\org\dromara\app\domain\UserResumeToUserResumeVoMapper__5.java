package org.dromara.app.domain;

import io.github.linpeilie.AutoMapperConfig__139;
import io.github.linpeilie.BaseMapper;
import org.dromara.app.domain.bo.UserResumeBoToUserResumeMapper__5;
import org.dromara.app.domain.vo.UserResumeVo;
import org.dromara.app.domain.vo.UserResumeVoToUserResumeMapper__5;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__139.class,
    uses = {UserResumeVoToUserResumeMapper__5.class,UserResumeBoToUserResumeMapper__5.class},
    imports = {}
)
public interface UserResumeToUserResumeVoMapper__5 extends BaseMapper<UserResume, UserResumeVo> {
}
