package org.dromara.app.domain;

import io.github.linpeilie.AutoMapperConfig__139;
import io.github.linpeilie.BaseMapper;
import org.dromara.app.domain.bo.KnowledgeDocumentBoToKnowledgeDocumentMapper__5;
import org.dromara.app.domain.vo.KnowledgeDocumentVo;
import org.dromara.app.domain.vo.KnowledgeDocumentVoToKnowledgeDocumentMapper__5;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__139.class,
    uses = {KnowledgeDocumentBoToKnowledgeDocumentMapper__5.class,KnowledgeDocumentVoToKnowledgeDocumentMapper__5.class},
    imports = {}
)
public interface KnowledgeDocumentToKnowledgeDocumentVoMapper__5 extends BaseMapper<KnowledgeDocument, KnowledgeDocumentVo> {
}
