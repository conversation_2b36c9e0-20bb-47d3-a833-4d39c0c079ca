package org.dromara.app.domain.vo;

import io.github.linpeilie.AutoMapperConfig__139;
import io.github.linpeilie.BaseMapper;
import org.dromara.app.domain.VideoComment;
import org.dromara.app.domain.VideoCommentToVideoCommentVoMapper__5;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__139.class,
    uses = {VideoCommentToVideoCommentVoMapper__5.class,VideoCommentToVideoCommentVoMapper__5.class},
    imports = {}
)
public interface VideoCommentVoToVideoCommentMapper__5 extends BaseMapper<VideoCommentVo, VideoComment> {
}
