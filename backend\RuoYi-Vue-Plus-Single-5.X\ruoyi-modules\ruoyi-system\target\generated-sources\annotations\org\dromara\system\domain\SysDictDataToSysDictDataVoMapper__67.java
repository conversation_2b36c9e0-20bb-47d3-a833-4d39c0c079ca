package org.dromara.system.domain;

import io.github.linpeilie.AutoMapperConfig__138;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.bo.SysDictDataBoToSysDictDataMapper__67;
import org.dromara.system.domain.vo.SysDictDataVo;
import org.dromara.system.domain.vo.SysDictDataVoToSysDictDataMapper__67;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__138.class,
    uses = {SysDictDataBoToSysDictDataMapper__67.class,SysDictDataVoToSysDictDataMapper__67.class},
    imports = {}
)
public interface SysDictDataToSysDictDataVoMapper__67 extends BaseMapper<SysDictData, SysDictDataVo> {
}
