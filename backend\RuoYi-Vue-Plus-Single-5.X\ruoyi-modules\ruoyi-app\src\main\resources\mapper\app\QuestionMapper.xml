<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.app.mapper.QuestionMapper">

    <!-- 根据题库ID和分类查询题目列表 -->
    <select id="selectQuestionsByCategory" resultType="org.dromara.common.mybatis.core.domain.Question">
        SELECT
        question_id, bank_id, question_code, title, content,
        answer, analysis, difficulty, category, practice_count,
        correct_rate, comment_count, tags, sort, status
        FROM app_question
        <where>
            <if test="category != null and category != 'all' and category != ''">
                AND category = #{category}
            </if>
            <if test="bankId != null and bankId != ''">
                AND bank_id = #{bankId}
            </if>
            AND status = '0'
        </where>
        ORDER BY sort, question_id
        LIMIT #{pageSize} OFFSET #{pageNum}
    </select>

    <!-- 根据题库ID查询推荐题目 -->
    <select id="selectRecommendedQuestions" resultType="org.dromara.common.mybatis.core.domain.Question">
        SELECT question_id,
               bank_id,
               question_code,
               title,
               content,
               answer,
               analysis,
               difficulty,
               category,
               practice_count,
               correct_rate,
               comment_count,
               tags,
               sort,
               status
        FROM app_question
        WHERE bank_id = #{bankId}
          AND status = '0'
        ORDER BY practice_count DESC, correct_rate DESC
        LIMIT #{limit}
    </select>

    <!-- 根据题库ID查询所有分类 -->
    <select id="selectCategoriesByBankId" resultType="java.lang.String">
        SELECT DISTINCT category
        FROM app_question
        WHERE bank_id = #{bankId}
          AND status = '0'
        ORDER BY category
    </select>

</mapper>
