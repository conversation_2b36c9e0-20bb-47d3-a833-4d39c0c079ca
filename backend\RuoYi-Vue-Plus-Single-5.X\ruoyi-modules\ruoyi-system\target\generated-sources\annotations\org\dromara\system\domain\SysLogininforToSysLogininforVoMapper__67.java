package org.dromara.system.domain;

import io.github.linpeilie.AutoMapperConfig__138;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.bo.SysLogininforBoToSysLogininforMapper__67;
import org.dromara.system.domain.vo.SysLogininforVo;
import org.dromara.system.domain.vo.SysLogininforVoToSysLogininforMapper__67;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__138.class,
    uses = {SysLogininforBoToSysLogininforMapper__67.class,SysLogininforVoToSysLogininforMapper__67.class},
    imports = {}
)
public interface SysLogininforToSysLogininforVoMapper__67 extends BaseMapper<SysLogininfor, SysLogininforVo> {
}
