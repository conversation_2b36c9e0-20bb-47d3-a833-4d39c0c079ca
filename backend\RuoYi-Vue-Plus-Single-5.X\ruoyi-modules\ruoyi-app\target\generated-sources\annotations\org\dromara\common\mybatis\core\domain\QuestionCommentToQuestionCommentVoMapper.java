package org.dromara.common.mybatis.core.domain;

import io.github.linpeilie.AutoMapperConfig__139;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.bo.QuestionCommentBoToQuestionCommentMapper;
import org.dromara.system.domain.vo.QuestionCommentVo;
import org.dromara.system.domain.vo.QuestionCommentVoToQuestionCommentMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__139.class,
    uses = {QuestionCommentBoToQuestionCommentMapper.class,QuestionCommentVoToQuestionCommentMapper.class},
    imports = {}
)
public interface QuestionCommentToQuestionCommentVoMapper extends BaseMapper<QuestionComment, QuestionCommentVo> {
}
