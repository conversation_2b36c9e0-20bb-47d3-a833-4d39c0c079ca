package org.dromara.app.domain.bo;

import javax.annotation.processing.Generated;
import org.dromara.app.domain.VideoComment;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-01T21:52:42+0800",
    comments = "version: 1.5.5.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250729-0351, environment: Java 21.0.8 (Eclipse Adoptium)"
)
@Component
public class VideoCommentBoToVideoCommentMapper__4Impl implements VideoCommentBoToVideoCommentMapper__4 {

    @Override
    public VideoComment convert(VideoCommentBo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        VideoComment videoComment = new VideoComment();

        videoComment.setContent( arg0.getContent() );
        videoComment.setId( arg0.getId() );
        videoComment.setParentId( arg0.getParentId() );
        videoComment.setUserId( arg0.getUserId() );
        videoComment.setVideoId( arg0.getVideoId() );

        return videoComment;
    }

    @Override
    public VideoComment convert(VideoCommentBo arg0, VideoComment arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setContent( arg0.getContent() );
        arg1.setId( arg0.getId() );
        arg1.setParentId( arg0.getParentId() );
        arg1.setUserId( arg0.getUserId() );
        arg1.setVideoId( arg0.getVideoId() );

        return arg1;
    }
}
