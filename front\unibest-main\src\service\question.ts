/**
 * 题目相关API服务
 */
import { httpGet, httpPost, httpPut, httpDelete } from '@/utils/http'
import type { ApiResponse } from '@/types/learning'
import type {
  QuestionDetail,
  Comment,
  CommentCreate,
  CommentQueryParams,
  CommentListResponse,
  QuestionBookmarkParams,
  QuestionPracticeRecord,
  QuestionStats,
} from '@/types/question'

/**
 * 题目数据类型定义
 */
export interface Question {
  questionId: number
  bankId: number
  bankTitle?: string
  questionCode: string
  title: string
  description?: string
  content: string
  answer?: string
  analysis?: string
  difficulty: 1 | 2 | 3 // 1-简单 2-中等 3-困难
  category?: string
  type: 1 | 2 | 3 | 4 | 5 // 1-单选题 2-多选题 3-判断题 4-简答题 5-编程题
  practiceCount: number
  correctRate: number
  acceptanceRate: number
  commentCount: number
  tags?: string
  sort: number
  status: '0' | '1' // 0-正常 1-停用
  remark?: string
  createTime: string
  updateTime: string
  isBookmarked?: boolean
  userAnswerStatus?: string
}

export interface QuestionQuery {
  bankId?: number
  questionCode?: string
  title?: string
  category?: string
  difficulty?: number
  type?: number
  status?: string
  page?: number
  pageSize?: number
}

export interface QuestionCreate {
  bankId: number
  questionCode: string
  title: string
  description?: string
  content: string
  answer?: string
  analysis?: string
  difficulty: 1 | 2 | 3
  category?: string
  type: 1 | 2 | 3 | 4 | 5
  tags?: string
  sort?: number
  status?: '0' | '1'
  remark?: string
}

export interface QuestionUpdate extends QuestionCreate {
  questionId: number
}

export interface PageResult<T> {
  records: T[]
  total: number
  size: number
  current: number
  pages: number
}

/**
 * 题目API服务类
 */
export const questionApi = {
  /**
   * 获取题目详情
   * @param questionId 题目ID
   * @returns 题目详情响应
   */
  getDetail(questionId: string): Promise<ApiResponse<QuestionDetail>> {
    return httpGet<QuestionDetail>(`/app/learning/questions/${questionId}`)
  },

  /**
   * 切换题目收藏状态
   * @param params 收藏参数
   * @returns 收藏状态响应
   */
  toggleBookmark(params: QuestionBookmarkParams): Promise<
    ApiResponse<{
      isBookmarked: boolean
      message: string
    }>
  > {
    return httpPost<{
      isBookmarked: boolean
      message: string
    }>(`/app/learning/questions/${params.questionId}/bookmark`, {
      isBookmarked: params.isBookmarked,
    })
  },

  /**
   * 获取题目评论列表
   * @param params 查询参数
   * @returns 评论列表响应
   */
  getComments(params: CommentQueryParams): Promise<ApiResponse<CommentListResponse>> {
    return httpGet<CommentListResponse>(`/app/learning/questions/${params.questionId}/comments`, {
      page: params.page || 1,
      pageSize: params.pageSize || 10,
      orderBy: params.orderBy || 'createTime',
      orderDirection: params.orderDirection || 'desc',
    })
  },

  /**
   * 创建评论
   * @param params 评论创建参数
   * @returns 创建结果响应
   */
  createComment(params: CommentCreate): Promise<ApiResponse<Comment>> {
    return httpPost<Comment>(`/app/learning/questions/${params.questionId}/comments`, {
      content: params.content,
      parentId: params.parentId,
    })
  },

  /**
   * 删除评论
   * @param commentId 评论ID
   * @returns 删除结果响应
   */
  deleteComment(commentId: string): Promise<ApiResponse<{ message: string }>> {
    return httpPost<{ message: string }>(`/app/learning/comments/${commentId}/delete`)
  },

  /**
   * 点赞评论
   * @param commentId 评论ID
   * @returns 点赞结果响应
   */
  likeComment(commentId: string): Promise<
    ApiResponse<{
      isLiked: boolean
      likeCount: number
      message?: string
    }>
  > {
    return httpPost<{
      isLiked: boolean
      likeCount: number
      message?: string
    }>(`/app/learning/comments/${commentId}/like`)
  },

  /**
   * 提交题目练习记录
   * @param record 练习记录
   * @returns 提交结果响应
   */
  submitPracticeRecord(record: Omit<QuestionPracticeRecord, 'id' | 'createTime'>): Promise<
    ApiResponse<{
      isCorrect: boolean
      message: string
      analysis?: string
    }>
  > {
    return httpPost<{
      isCorrect: boolean
      message: string
      analysis?: string
    }>(`/app/learning/questions/${record.questionId}/practice`, {
      userAnswer: record.userAnswer,
      timeSpent: record.timeSpent,
    })
  },

  /**
   * 获取题目统计信息
   * @param questionId 题目ID
   * @returns 统计信息响应
   */
  getStats(questionId: string): Promise<ApiResponse<QuestionStats>> {
    return httpGet<QuestionStats>(`/app/learning/questions/${questionId}/stats`)
  },

  /**
   * 获取相关题目推荐
   * @param questionId 题目ID
   * @param limit 推荐数量限制
   * @returns 相关题目列表响应
   */
  getRelatedQuestions(questionId: string, limit = 5): Promise<ApiResponse<QuestionDetail[]>> {
    return httpGet<QuestionDetail[]>(`/app/learning/questions/${questionId}/related`, {
      limit,
    })
  },

  /**
   * 举报题目或评论
   * @param params 举报参数
   * @returns 举报结果响应
   */
  report(params: {
    targetId: string
    targetType: 'question' | 'comment'
    reason: string
    description?: string
  }): Promise<ApiResponse<{ message: string }>> {
    return httpPost<{ message: string }>('/app/learning/report', params)
  },

  // ========== 新增的系统管理API ==========

  /**
   * 分页查询题目列表
   * @param params 查询参数
   * @returns 题目分页列表
   */
  getList(params: QuestionQuery): Promise<ApiResponse<PageResult<Question>>> {
    return httpGet<PageResult<Question>>('/system/question/list', params)
  },

  /**
   * 根据题库ID查询题目列表
   * @param bankId 题库ID
   * @param params 查询参数
   * @returns 题目分页列表
   */
  getListByBankId(bankId: number, params: QuestionQuery): Promise<ApiResponse<PageResult<Question>>> {
    return httpGet<PageResult<Question>>(`/system/question/bank/${bankId}`, params)
  },

  /**
   * 获取题目详情（系统管理）
   * @param questionId 题目ID
   * @returns 题目详情
   */
  getDetailById(questionId: number): Promise<ApiResponse<Question>> {
    return httpGet<Question>(`/system/question/${questionId}`)
  },

  /**
   * 新增题目
   * @param data 题目数据
   * @returns 创建结果
   */
  create(data: QuestionCreate): Promise<ApiResponse<void>> {
    return httpPost<void>('/system/question', data)
  },

  /**
   * 修改题目
   * @param data 题目数据
   * @returns 修改结果
   */
  update(data: QuestionUpdate): Promise<ApiResponse<void>> {
    return httpPut<void>('/system/question', data)
  },

  /**
   * 删除题目
   * @param questionIds 题目ID数组
   * @returns 删除结果
   */
  delete(questionIds: number[]): Promise<ApiResponse<void>> {
    return httpDelete<void>(`/system/question/${questionIds.join(',')}`)
  },

  /**
   * 根据题目编码查询题目
   * @param questionCode 题目编码
   * @returns 题目信息
   */
  getByCode(questionCode: string): Promise<ApiResponse<Question>> {
    return httpGet<Question>(`/system/question/code/${questionCode}`)
  },

  /**
   * 根据分类查询题目列表
   * @param category 分类
   * @returns 题目列表
   */
  getByCategory(category: string): Promise<ApiResponse<Question[]>> {
    return httpGet<Question[]>(`/system/question/category/${category}`)
  },

  /**
   * 根据难度查询题目列表
   * @param difficulty 难度
   * @returns 题目列表
   */
  getByDifficulty(difficulty: number): Promise<ApiResponse<Question[]>> {
    return httpGet<Question[]>(`/system/question/difficulty/${difficulty}`)
  },

  /**
   * 根据题目类型查询题目列表
   * @param type 题目类型
   * @returns 题目列表
   */
  getByType(type: number): Promise<ApiResponse<Question[]>> {
    return httpGet<Question[]>(`/system/question/type/${type}`)
  },

  /**
   * 查询用户收藏的题目列表
   * @param userId 用户ID
   * @returns 收藏题目列表
   */
  getBookmarkedQuestions(userId: number): Promise<ApiResponse<Question[]>> {
    return httpGet<Question[]>(`/system/question/bookmarked/${userId}`)
  },

  /**
   * 查询热门题目列表
   * @param limit 限制数量
   * @returns 热门题目列表
   */
  getHotQuestions(limit = 10): Promise<ApiResponse<Question[]>> {
    return httpGet<Question[]>('/system/question/hot', { limit })
  },

  /**
   * 随机查询题目列表
   * @param bankId 题库ID
   * @param limit 限制数量
   * @returns 随机题目列表
   */
  getRandomQuestions(bankId: number, limit = 10): Promise<ApiResponse<Question[]>> {
    return httpGet<Question[]>('/system/question/random', { bankId, limit })
  },

  /**
   * 更新题目练习次数
   * @param questionId 题目ID
   * @returns 更新结果
   */
  updatePracticeCount(questionId: number): Promise<ApiResponse<void>> {
    return httpPut<void>(`/system/question/practice/${questionId}`)
  },

  /**
   * 更新题目评论数
   * @param questionId 题目ID
   * @returns 更新结果
   */
  updateCommentCount(questionId: number): Promise<ApiResponse<void>> {
    return httpPut<void>(`/system/question/comment/${questionId}`)
  },

  /**
   * 更新题目正确率
   * @param questionId 题目ID
   * @param correctRate 正确率
   * @returns 更新结果
   */
  updateCorrectRate(questionId: number, correctRate: number): Promise<ApiResponse<void>> {
    return httpPut<void>('/system/question/correctRate', null, { questionId, correctRate })
  },

  /**
   * 启用/停用题目
   * @param questionId 题目ID
   * @param status 状态
   * @returns 操作结果
   */
  changeStatus(questionId: number, status: '0' | '1'): Promise<ApiResponse<void>> {
    return httpPut<void>('/system/question/status', null, { questionId, status })
  },

  /**
   * 复制题目
   * @param questionId 源题目ID
   * @param title 新题目标题
   * @returns 复制结果
   */
  copyQuestion(questionId: number, title: string): Promise<ApiResponse<void>> {
    return httpPost<void>('/system/question/copy', null, { questionId, title })
  },

  /**
   * 统计题库下的题目数量
   * @param bankId 题库ID
   * @returns 题目数量
   */
  countByBankId(bankId: number): Promise<ApiResponse<number>> {
    return httpGet<number>(`/system/question/count/${bankId}`)
  },

  /**
   * 导出题目数据
   * @param params 查询参数
   * @returns 导出结果
   */
  exportData(params: QuestionQuery): Promise<ApiResponse<void>> {
    return httpPost<void>('/system/question/export', params)
  },

  /**
   * 导入题目数据
   * @param file 文件
   * @returns 导入结果
   */
  importData(file: File): Promise<ApiResponse<string>> {
    const formData = new FormData()
    formData.append('file', file)
    return httpPost<string>('/system/question/importData', formData)
  },

  /**
   * 获取导入模板
   * @returns 模板文件
   */
  getImportTemplate(): Promise<ApiResponse<void>> {
    return httpPost<void>('/system/question/importTemplate')
  },
}
