package org.dromara.system.domain;

import io.github.linpeilie.AutoMapperConfig__138;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.bo.SysClientBoToSysClientMapper__67;
import org.dromara.system.domain.vo.SysClientVo;
import org.dromara.system.domain.vo.SysClientVoToSysClientMapper__67;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__138.class,
    uses = {SysClientVoToSysClientMapper__67.class,SysClientBoToSysClientMapper__67.class},
    imports = {}
)
public interface SysClientToSysClientVoMapper__67 extends BaseMapper<SysClient, SysClientVo> {
}
