package org.dromara.system.domain;

import io.github.linpeilie.AutoMapperConfig__138;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.bo.SysRoleBoToSysRoleMapper__30;
import org.dromara.system.domain.vo.SysRoleVo;
import org.dromara.system.domain.vo.SysRoleVoToSysRoleMapper__30;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__138.class,
    uses = {SysRoleVoToSysRoleMapper__30.class,SysRoleBoToSysRoleMapper__30.class},
    imports = {}
)
public interface SysRoleToSysRoleVoMapper__30 extends BaseMapper<SysRole, SysRoleVo> {
}
