package org.dromara.system.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;
import org.dromara.app.domain.Question;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * 题目视图对象
 *
 * <AUTHOR>
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = Question.class)
public class QuestionVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 题目ID
     */
    @ExcelProperty(value = "题目ID")
    private Long questionId;

    /**
     * 题库ID
     */
    @ExcelProperty(value = "题库ID")
    private Long bankId;

    /**
     * 题库标题（关联查询）
     */
    @ExcelProperty(value = "题库标题")
    private String bankTitle;

    /**
     * 题目编码
     */
    @ExcelProperty(value = "题目编码")
    private String questionCode;

    /**
     * 题目标题
     */
    @ExcelProperty(value = "题目标题")
    private String title;

    /**
     * 题目描述
     */
    @ExcelProperty(value = "题目描述")
    private String description;

    /**
     * 题目内容
     */
    @ExcelProperty(value = "题目内容")
    private String content;

    /**
     * 参考答案
     */
    @ExcelProperty(value = "参考答案")
    private String answer;

    /**
     * 答案解析
     */
    @ExcelProperty(value = "答案解析")
    private String analysis;

    /**
     * 难度（1-简单 2-中等 3-困难）
     */
    @ExcelProperty(value = "难度", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "question_difficulty")
    private Integer difficulty;

    /**
     * 分类
     */
    @ExcelProperty(value = "分类")
    private String category;

    /**
     * 题目类型（1-单选题 2-多选题 3-判断题 4-简答题 5-编程题）
     */
    @ExcelProperty(value = "题目类型", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "question_type")
    private Integer type;

    /**
     * 练习次数
     */
    @ExcelProperty(value = "练习次数")
    private Integer practiceCount;

    /**
     * 正确率
     */
    @ExcelProperty(value = "正确率")
    private Integer correctRate;

    /**
     * 通过率（百分比）
     */
    @ExcelProperty(value = "通过率")
    private Double acceptanceRate;

    /**
     * 评论数
     */
    @ExcelProperty(value = "评论数")
    private Integer commentCount;

    /**
     * 标签（JSON格式）
     */
    @ExcelProperty(value = "标签")
    private String tags;

    /**
     * 排序
     */
    @ExcelProperty(value = "排序")
    private Integer sort;

    /**
     * 状态（0正常 1停用）
     */
    @ExcelProperty(value = "状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "sys_normal_disable")
    private String status;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;

    /**
     * 创建时间
     */
    @ExcelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 更新时间
     */
    @ExcelProperty(value = "更新时间")
    private Date updateTime;

    /**
     * 是否收藏（非数据库字段）
     */
    private Boolean isBookmarked;

    /**
     * 用户答题状态（非数据库字段）
     */
    private String userAnswerStatus;
}
