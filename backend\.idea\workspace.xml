<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="e231f2c9-3c03-4727-9b0e-a28dafc3c073" name="更改" comment="">
      <change beforePath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-modules/ruoyi-app/src/main/java/org/dromara/app/domain/vo/QuestionBankVo.java" beforeDir="false" afterPath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-modules/ruoyi-app/src/main/java/org/dromara/app/domain/vo/QuestionBankVo.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-modules/ruoyi-app/src/main/java/org/dromara/app/domain/vo/QuestionVO.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../front/unibest-main/.eslintrc-auto-import.json" beforeDir="false" afterPath="$PROJECT_DIR$/../front/unibest-main/.eslintrc-auto-import.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../front/unibest-main/package.json" beforeDir="false" afterPath="$PROJECT_DIR$/../front/unibest-main/package.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../front/unibest-main/pnpm-lock.yaml" beforeDir="false" afterPath="$PROJECT_DIR$/../front/unibest-main/pnpm-lock.yaml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../front/unibest-main/src/manifest.json" beforeDir="false" afterPath="$PROJECT_DIR$/../front/unibest-main/src/manifest.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../front/unibest-main/src/pages.json" beforeDir="false" afterPath="$PROJECT_DIR$/../front/unibest-main/src/pages.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../front/unibest-main/src/service/dataManager.ts" beforeDir="false" afterPath="$PROJECT_DIR$/../front/unibest-main/src/service/dataManager.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../front/unibest-main/src/service/interview-select.ts" beforeDir="false" afterPath="$PROJECT_DIR$/../front/unibest-main/src/service/interview-select.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../front/unibest-main/src/types/auto-import.d.ts" beforeDir="false" afterPath="$PROJECT_DIR$/../front/unibest-main/src/types/auto-import.d.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../front/unibest-main/src/types/onboarding.ts" beforeDir="false" afterPath="$PROJECT_DIR$/../front/unibest-main/src/types/onboarding.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../front/unibest-main/src/types/uni-pages.d.ts" beforeDir="false" afterPath="$PROJECT_DIR$/../front/unibest-main/src/types/uni-pages.d.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../front/unibest-main/tsconfig.json" beforeDir="false" afterPath="$PROJECT_DIR$/../front/unibest-main/tsconfig.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../front/unibest-main/vite.config.ts" beforeDir="false" afterPath="$PROJECT_DIR$/../front/unibest-main/vite.config.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../target/classes/META-INF/mps/autoMapper" beforeDir="false" afterPath="$PROJECT_DIR$/../target/classes/META-INF/mps/autoMapper" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../target/classes/META-INF/mps/mappers" beforeDir="false" afterPath="$PROJECT_DIR$/../target/classes/META-INF/mps/mappers" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$/.." />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 2
}</component>
  <component name="ProjectId" id="30g6d7k07sR3ffn9XX2yhtgcZDM" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "RequestMappingsPanelOrder0": "0",
    "RequestMappingsPanelOrder1": "1",
    "RequestMappingsPanelWidth0": "75",
    "RequestMappingsPanelWidth1": "75",
    "RunOnceActivity.OpenProjectViewOnStart": "true",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "Spring Boot.DromaraApplication.executor": "Run",
    "git-widget-placeholder": "master",
    "kotlin-language-version-configured": "true",
    "last_opened_file_path": "C:/Users/<USER>/Desktop/softwart-xunfei-code2/backend",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "vue.rearranger.settings.migration": "true"
  }
}]]></component>
  <component name="RunManager" selected="Spring Boot.DromaraApplication">
    <configuration name="DromaraApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="ruoyi-admin" />
      <option name="SHORTEN_COMMAND_LINE" value="ARGS_FILE" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="org.dromara.DromaraApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration default="true" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot">
      <option name="SHORTEN_COMMAND_LINE" value="ARGS_FILE" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="jdk-17.0.10-corretto-17.0.10-f644763e9732-24fca987" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="应用程序级" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="e231f2c9-3c03-4727-9b0e-a28dafc3c073" name="更改" comment="" />
      <created>1754036953775</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1754036953775</updated>
      <workItem from="1754036954851" duration="476000" />
      <workItem from="1754052301428" duration="2037000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
</project>