<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { onLoad, onShow, onHide, onUnload } from '@dcloudio/uni-app'
// @ts-ignore
import HeadBar from '@/components/HeadBar.vue'
// @ts-ignore
import LoadingSpinner from '@/components/LoadingSpinner.vue'
import MarkdownIt from 'markdown-it'
import hljs from 'highlight.js'
import 'highlight.js/styles/github.css'
// @ts-ignore
import type { QuestionDetail, Comment } from '@/types/question'
// @ts-ignore
import { questionApi } from '@/service/question'
import { questionCommentApi, type QuestionComment } from '@/service/questionComment'
// 导入学习时长记录工具
// @ts-ignore
import { activityTimer, ActivityType } from '@/utils/studyTimer'

/**
 * 题目详情展示页面，包含题目信息、答案解析、评论讨论等功能
 * 功能包括：
 * - 显示题目详情和解析
 * - 支持Markdown渲染
 * - 评论功能（发表、查看、点赞）
 * - 收藏功能
 * - 分享功能
 */

// 路由参数
const questionId = ref('')
const bankId = ref('')
const questionIndex = ref(0)
const totalQuestions = ref(0)

// 题目详情数据结构
const question = ref<QuestionDetail>({
  id: '',
  title: '',
  content: '',
  answer: '',
  analysis: '',
  difficulty: '中等',
  tags: [],
  practiceCount: 0,
  correctRate: 0,
  commentCount: 0,
  isBookmarked: false,
  questionType: 'single',
  options: [],
  correctAnswer: '',
  createTime: '',
  updateTime: '',
})

// 页面状态控制
const showAnswer = ref(false)
const isLoading = ref(true)
const isSubmittingComment = ref(false)
const isLikingComment = ref(false)
const isNavigating = ref(false)

// 评论列表数据
const comments = ref<Comment[]>([])
const showCommentInput = ref(false)
const newComment = ref('')

// 学习时长记录相关状态
const isTimerActive = ref(false)
const studyStartTime = ref<number>(0)
const currentStudyTime = ref<string>('0分钟')
const studyTimeInterval = ref<number | null>(null)

// Markdown渲染器初始化
const md = new MarkdownIt({
  html: true, // 允许HTML标签
  linkify: true, // 自动识别链接
  typographer: true, // 启用一些语言中性的替换和引号美化
  highlight: function (str, lang) {
    if (lang && hljs.getLanguage(lang)) {
      try {
        return (
          '<pre class="hljs"><code>' +
          hljs.highlight(str, { language: lang, ignoreIllegals: true }).value +
          '</code></pre>'
        )
      } catch (__) {}
    }
    return '<pre class="hljs"><code>' + md.utils.escapeHtml(str) + '</code></pre>'
  },
})

/**
 * 渲染题目描述的Markdown内容
 * @description 使用Markdown渲染器处理题目内容
 * @returns 渲染后的HTML字符串
 */
const renderedContent = computed(() => {
  return md.render(question.value.content || '')
})

/**
 * 渲染参考答案的Markdown内容
 * @description 使用Markdown渲染器处理答案内容
 * @returns 渲染后的HTML字符串
 */
const renderedAnswer = computed(() => {
  return md.render(question.value.answer || '')
})

/**
 * 渲染知识点解析的Markdown内容
 * @description 使用Markdown渲染器处理解析内容
 * @returns 渲染后的HTML字符串
 */
const renderedAnalysis = computed(() => {
  return md.render(question.value.analysis || '')
})

/**
 * 根据题目难度返回对应的样式类名
 * @description 为不同难度级别返回对应的CSS类名
 * @param difficulty 题目难度（简单/中等/困难）
 * @returns 样式类名字符串
 */
const getDifficultyStyle = (difficulty: string) => {
  const styles = {
    简单: 'difficulty-easy',
    中等: 'difficulty-medium',
    困难: 'difficulty-hard',
  }
  return styles[difficulty] || 'difficulty-default'
}

/**
 * 切换题目收藏状态
 * @description 调用API切换收藏状态并更新本地状态
 */
const toggleBookmark = async () => {
  try {
    uni.showLoading({ title: '处理中...' })

    const response = await questionApi.toggleBookmark({
      questionId: questionId.value,
      isBookmarked: !question.value.isBookmarked,
    })

    if (response.code === 200) {
      question.value.isBookmarked = response.data.isBookmarked

      uni.showToast({
        title: response.data.message || (question.value.isBookmarked ? '收藏成功' : '取消收藏'),
        icon: 'success',
        duration: 1500,
      })
    } else {
      throw new Error(response.message || '操作失败')
    }
  } catch (error) {
    console.error('切换收藏状态失败:', error)
    uni.showToast({
      title: '操作失败，请重试',
      icon: 'none',
      duration: 2000,
    })
  } finally {
    uni.hideLoading()
  }
}

/**
 * 切换答案显示/隐藏状态
 * @description 控制参考答案的显示和隐藏
 */
const toggleAnswer = () => {
  showAnswer.value = !showAnswer.value

  // 统计用户查看答案行为
  if (showAnswer.value) {
    console.log('用户查看了答案')

    // 记录查看答案的行为到学习时长记录的元数据中
    if (isTimerActive.value) {
      // 可以通过更新活动的元数据来记录用户行为
      console.log('记录用户查看答案行为')
    }
  }
}

/**
 * 更新当前学习时长显示
 * @description 定时更新页面上显示的学习时长
 */
const updateStudyTimeDisplay = () => {
  if (isTimerActive.value && studyStartTime.value > 0) {
    const duration = Date.now() - studyStartTime.value
    currentStudyTime.value = activityTimer.formatDuration(duration)
  }
}

/**
 * 开始学习时长记录
 * @description 开始记录用户在题目详情页面的学习时长
 */
const startStudyTimer = async () => {
  try {
    if (!isTimerActive.value) {
      await activityTimer.startActivity(ActivityType.EXERCISE, {
        activityId: questionId.value,
        activityName: question.value.title || '题目学习',
        categoryId: bankId.value,
        categoryName: '题库练习',
        metadata: {
          questionType: question.value.questionType,
          difficulty: question.value.difficulty,
          tags: question.value.tags
        }
      })

      isTimerActive.value = true
      studyStartTime.value = Date.now()

      // 开始定时更新学习时长显示
      studyTimeInterval.value = window.setInterval(updateStudyTimeDisplay, 1000)

      console.log('开始记录题目学习时长:', questionId.value)
    }
  } catch (error) {
    console.error('启动学习时长记录失败:', error)
  }
}

/**
 * 暂停学习时长记录
 * @description 暂停记录学习时长（页面隐藏时调用）
 */
const pauseStudyTimer = async () => {
  try {
    if (isTimerActive.value) {
      await activityTimer.pauseActivity()

      // 清除定时器
      if (studyTimeInterval.value) {
        clearInterval(studyTimeInterval.value)
        studyTimeInterval.value = null
      }

      console.log('暂停题目学习时长记录')
    }
  } catch (error) {
    console.error('暂停学习时长记录失败:', error)
  }
}

/**
 * 恢复学习时长记录
 * @description 恢复记录学习时长（页面显示时调用）
 */
const resumeStudyTimer = () => {
  try {
    if (isTimerActive.value) {
      activityTimer.resumeActivity()

      // 重新开始定时更新显示
      if (!studyTimeInterval.value) {
        studyTimeInterval.value = window.setInterval(updateStudyTimeDisplay, 1000)
      }

      console.log('恢复题目学习时长记录')
    }
  } catch (error) {
    console.error('恢复学习时长记录失败:', error)
  }
}

/**
 * 结束学习时长记录
 * @description 结束记录学习时长（页面卸载时调用）
 */
const endStudyTimer = async () => {
  try {
    if (isTimerActive.value) {
      await activityTimer.endActivity()
      isTimerActive.value = false

      // 清除定时器
      if (studyTimeInterval.value) {
        clearInterval(studyTimeInterval.value)
        studyTimeInterval.value = null
      }

      const studyDuration = Date.now() - studyStartTime.value
      console.log('结束题目学习时长记录，总时长:', activityTimer.formatDuration(studyDuration))
    }
  } catch (error) {
    console.error('结束学习时长记录失败:', error)
  }
}

/**
 * 切换到上一题
 * @description 导航到上一题的详情页面
 */
const goToPreviousQuestion = async () => {
  if (questionIndex.value <= 0) {
    uni.showToast({
      title: '已经是第一题了',
      icon: 'none',
    })
    return
  }

  if (isNavigating.value) return
  isNavigating.value = true

  try {
    // 这里应该调用API获取上一题的ID
    // 暂时使用模拟逻辑
    const prevQuestionId = await getPreviousQuestionId()
    if (prevQuestionId) {
      uni.redirectTo({
        url: `/pages/learning/question-detail?id=${prevQuestionId}&bankId=${bankId.value}&index=${questionIndex.value - 1}&total=${totalQuestions.value}`,
      })
    }
  } catch (error) {
    console.error('切换到上一题失败:', error)
    uni.showToast({
      title: '切换失败，请重试',
      icon: 'none',
    })
  } finally {
    isNavigating.value = false
  }
}

/**
 * 切换到下一题
 * @description 导航到下一题的详情页面
 */
const goToNextQuestion = async () => {
  if (questionIndex.value >= totalQuestions.value - 1) {
    uni.showToast({
      title: '已经是最后一题了',
      icon: 'none',
    })
    return
  }

  if (isNavigating.value) return
  isNavigating.value = true

  try {
    // 这里应该调用API获取下一题的ID
    // 暂时使用模拟逻辑
    const nextQuestionId = await getNextQuestionId()
    if (nextQuestionId) {
      uni.redirectTo({
        url: `/pages/learning/question-detail?id=${nextQuestionId}&bankId=${bankId.value}&index=${questionIndex.value + 1}&total=${totalQuestions.value}`,
      })
    }
  } catch (error) {
    console.error('切换到下一题失败:', error)
    uni.showToast({
      title: '切换失败，请重试',
      icon: 'none',
    })
  } finally {
    isNavigating.value = false
  }
}

/**
 * 获取上一题ID（模拟API调用）
 * @description 根据当前题库和题目索引获取上一题ID
 */
const getPreviousQuestionId = async (): Promise<string | null> => {
  // TODO: 实现真实的API调用
  // const response = await questionApi.getPreviousQuestion(bankId.value, questionId.value)
  // return response.data?.id || null

  // 模拟延迟
  await new Promise(resolve => setTimeout(resolve, 500))
  return `question_${questionIndex.value - 1}`
}

/**
 * 获取下一题ID（模拟API调用）
 * @description 根据当前题库和题目索引获取下一题ID
 */
const getNextQuestionId = async (): Promise<string | null> => {
  // TODO: 实现真实的API调用
  // const response = await questionApi.getNextQuestion(bankId.value, questionId.value)
  // return response.data?.id || null

  // 模拟延迟
  await new Promise(resolve => setTimeout(resolve, 500))
  return `question_${questionIndex.value + 1}`
}

/**
 * 开始练习当前题目
 * @description 跳转到题目练习页面，并结束当前学习时长记录
 */
const startPractice = async () => {
  if (!questionId.value) {
    uni.showToast({
      title: '题目ID无效',
      icon: 'none',
    })
    return
  }

  // 在跳转到练习页面前结束当前的学习时长记录
  if (isTimerActive.value) {
    await endStudyTimer()
    console.log('用户开始练习，结束题目详情页面的学习时长记录')
  }

  uni.navigateTo({
    url: `/pages/learning/practice-question?id=${questionId.value}&bankId=${bankId.value}&index=${questionIndex.value}&total=${totalQuestions.value}`,
    fail: (error) => {
      console.error('跳转失败:', error)
      uni.showToast({
        title: '页面跳转失败',
        icon: 'none',
      })
    },
  })
}

/**
 * 分享当前题目
 * @description 分享题目链接（H5端复制链接，小程序端调用分享）
 */
const shareQuestion = () => {
  // #ifdef H5
  const url = window.location.href
  navigator.clipboard
    .writeText(url)
    .then(() => {
      uni.showToast({
        title: '分享链接已复制',
        icon: 'success',
        duration: 1500,
      })
    })
    .catch(() => {
      uni.showToast({
        title: '复制失败，请手动复制',
        icon: 'none',
      })
    })
  // #endif

  // #ifdef MP-WEIXIN
  uni.showShareMenu({
    withShareTicket: true,
    success: () => {
      console.log('分享菜单显示成功')
    },
    fail: () => {
      uni.showToast({
        title: '分享功能暂不可用',
        icon: 'none',
      })
    },
  })
  // #endif
}

/**
 * 查看所有评论
 * @description 跳转到评论详情页面
 */
const viewAllComments = () => {
  if (!questionId.value) {
    uni.showToast({
      title: '题目ID无效',
      icon: 'none',
    })
    return
  }

  uni.navigateTo({
    url: `/pages/learning/question-comments?id=${questionId.value}`,
    fail: (error) => {
      console.error('跳转失败:', error)
      uni.showToast({
        title: '页面跳转失败',
        icon: 'none',
      })
    },
  })
}

/**
 * 发表评论
 * @description 提交新评论并更新评论列表
 */
const submitComment = async () => {
  if (!newComment.value.trim()) {
    uni.showToast({
      title: '请输入评论内容',
      icon: 'none',
    })
    return
  }

  if (newComment.value.length > 500) {
    uni.showToast({
      title: '评论内容不能超过500字',
      icon: 'none',
    })
    return
  }

  try {
    isSubmittingComment.value = true

    const response = await questionCommentApi.create({
      questionId: parseInt(questionId.value),
      content: newComment.value.trim(),
      userId: 1, // 这里应该从用户状态获取
    })

    if (response.code === 200) {
      // 重新加载评论列表
      await loadComments()
      question.value.commentCount++

      // 清空输入
      newComment.value = ''
      showCommentInput.value = false

      uni.showToast({
        title: '评论成功',
        icon: 'success',
        duration: 1500,
      })
    } else {
      throw new Error(response.msg || '评论失败')
    }
  } catch (error) {
    console.error('发表评论失败:', error)
    uni.showToast({
      title: error.message || '评论失败，请重试',
      icon: 'none',
      duration: 2000,
    })
  } finally {
    isSubmittingComment.value = false
  }
}

/**
 * 切换评论输入框显示
 * @description 控制评论输入框的显示和隐藏
 */
const toggleCommentInput = () => {
  showCommentInput.value = !showCommentInput.value

  // 如果显示输入框，清空之前的内容
  if (showCommentInput.value) {
    newComment.value = ''
  }
}

/**
 * 加载题目详情数据
 * @description 从API获取题目详情信息
 */
const loadQuestionData = async () => {
  try {
    isLoading.value = true

    const response = await questionApi.getDetail(questionId.value)

    if (response.code === 200) {
      question.value = response.data
      await loadComments()
    } else {
      throw new Error(response.message || '获取题目详情失败')
    }
  } catch (error) {
    console.error('加载题目数据失败:', error)
    uni.showToast({
      title: error.message || '加载失败，请重试',
      icon: 'none',
      duration: 2000,
    })

    // 3秒后返回上一页
    setTimeout(() => {
      uni.navigateBack({
        fail: () => {
          // 如果返回失败，跳转到首页
          uni.switchTab({
            url: '/pages/index/index',
          })
        },
      })
    }, 3000)
  } finally {
    isLoading.value = false
  }
}

/**
 * 加载评论数据 - 使用新API
 * @description 获取题目的评论列表（首页只显示前3条）
 */
const loadComments = async () => {
  try {
    const response = await questionCommentApi.getListByQuestionId(parseInt(questionId.value), {
      page: 1,
      pageSize: 3,
    })

    if (response.code === 200) {
      // 转换数据格式以兼容现有组件
      comments.value = response.data.records.map(item => ({
        id: item.commentId,
        userId: item.userId,
        userName: item.userName || '匿名用户',
        userAvatar: item.userAvatar || '',
        content: item.content,
        time: item.createTime,
        likes: item.likeCount,
        replies: item.replyCount,
        isLiked: item.isLiked || false,
      }))
    } else {
      console.warn('获取评论失败:', response.msg)
      comments.value = []
    }
  } catch (error) {
    console.error('加载评论数据失败:', error)
    comments.value = []
  }
}

/**
 * 点赞评论
 * @description 切换评论的点赞状态
 * @param commentId 评论ID
 */
const likeComment = async (commentId: string) => {
  if (isLikingComment.value) {
    return // 防止重复点击
  }

  try {
    isLikingComment.value = true

    const response = await questionCommentApi.likeComment(parseInt(commentId), 1) // 用户ID应该从状态获取
    console.log(response.data)

    if (response.code === 200) {
      // 更新本地评论数据
      const comment = comments.value.find((c) => c.id.toString() === commentId)
      if (comment) {
        comment.likes = (comment.likes || 0) + (comment.isLiked ? -1 : 1)
        comment.isLiked = !comment.isLiked
      }

      uni.showToast({
        title: comment?.isLiked ? '点赞成功' : '取消点赞',
        icon: 'success',
        duration: 1000,
      })
    } else {
      throw new Error(response.msg || '操作失败')
    }
  } catch (error: any) {
    console.error('点赞操作失败:', error)
    uni.showToast({
      title: error.message || '操作失败，请重试',
      icon: 'none',
      duration: 1500,
    })
  } finally {
    isLikingComment.value = false
  }
}

/**
 * 页面加载时的初始化处理
 * @description 获取页面参数并加载数据
 * @param options 页面参数
 */
onLoad(async (options: any) => {
  console.log('页面加载参数:', options)

  if (options && options.id) {
    questionId.value = options.id || ''
    bankId.value = options.bankId || ''
    questionIndex.value = parseInt(options.index || '0')
    totalQuestions.value = parseInt(options.total || '1')

    // 参数验证
    if (!questionId.value) {
      uni.showToast({
        title: '题目ID不能为空',
        icon: 'none',
      })
      setTimeout(() => {
        uni.navigateBack()
      }, 1500)
      return
    }

    // 加载题目数据
    await loadQuestionData()

    // 数据加载完成后开始学习时长记录
    if (question.value.id) {
      await startStudyTimer()
    }
  } else {
    uni.showToast({
      title: '参数错误',
      icon: 'none',
    })
    setTimeout(() => {
      uni.navigateBack()
    }, 1500)
  }
})

/**
 * 页面显示时的处理
 * @description 页面每次显示时重新加载评论数据并恢复学习时长记录
 */
onShow(() => {
  // 如果页面已经加载过，重新加载评论数据
  if (questionId.value && !isLoading.value) {
    loadComments()
  }

  // 恢复学习时长记录
  if (isTimerActive.value) {
    resumeStudyTimer()
  }
})

/**
 * 页面隐藏时的处理
 * @description 页面隐藏时暂停学习时长记录
 */
onHide(() => {
  // 暂停学习时长记录
  if (isTimerActive.value) {
    pauseStudyTimer()
  }
})

/**
 * 页面卸载时的处理
 * @description 页面卸载时结束学习时长记录
 */
onUnload(() => {
  // 结束学习时长记录
  if (isTimerActive.value) {
    endStudyTimer()
  }
})
</script>

<template>
  <view class="question-detail-page">
    <HeadBar title="题目详情" :show-back="true" />

    <!-- 加载状态 -->
    <LoadingSpinner v-if="isLoading" />

    <!-- 主要内容 -->
    <scroll-view v-else scroll-y class="main-scroll" :style="{ height: 'calc(100vh - 88rpx)' }">
      <view class="content-container">
        <!-- 题目头部信息卡片 -->
        <view class="question-header-card">
          <!-- 难度标签和操作按钮 -->
          <view class="header-top">
            <view class="difficulty-tag" :class="getDifficultyStyle(question.difficulty)">
              <text class="difficulty-text">{{ question.difficulty }}</text>
            </view>
            <view class="action-buttons">
              <view
                class="action-btn bookmark-btn"
                :class="{ active: question.isBookmarked }"
                @click="toggleBookmark"
              >
                <view
                  class="action-icon"
                  :class="question.isBookmarked ? 'i-mdi-star' : 'i-mdi-star-outline'"
                ></view>
              </view>
              <view class="action-btn share-btn" @click="shareQuestion">
                <view class="action-icon i-mdi-share"></view>
              </view>
            </view>
          </view>

          <!-- 题目标题 -->
          <view class="question-title">
            <text class="title-text">{{ question.title }}</text>
          </view>

          <!-- 统计信息 -->
          <view class="stats-row">
            <view class="stat-item">
              <view class="stat-icon i-mdi-account-group"></view>
              <text class="stat-text">{{ question.practiceCount }} 人练习</text>
            </view>
            <view class="stat-item">
              <view class="stat-icon i-mdi-chart-bar"></view>
              <text class="stat-text">正确率 {{ question.correctRate }}%</text>
            </view>
            <view class="stat-item">
              <view class="stat-icon i-mdi-chat-outline"></view>
              <text class="stat-text">{{ question.commentCount }} 讨论</text>
            </view>
            <!-- 学习时长显示 -->
            <view v-if="isTimerActive" class="stat-item study-time-item">
              <view class="stat-icon i-mdi-clock-outline"></view>
              <text class="stat-text">学习时长 {{ currentStudyTime }}</text>
            </view>
          </view>

          <!-- 标签 -->
          <view class="tags-container">
            <view v-for="tag in question.tags" :key="tag" class="tag-item">
              <text class="tag-text">{{ tag || '无' }}</text>
            </view>
          </view>
        </view>

        <!-- 题目描述卡片 -->
        <view class="content-card">
          <view class="card-header">
            <view class="card-title-wrapper">
              <view class="card-icon i-mdi-text-box"></view>
              <text class="card-title">题目描述</text>
            </view>
          </view>
          <view class="question-content">
            <rich-text
              v-if="renderedContent"
              class="markdown-content"
              :nodes="renderedContent"
            ></rich-text>
            <text v-else class="no-content-text">暂无题目描述</text>
          </view>
        </view>

        <!-- 参考答案卡片 -->
        <view class="content-card">
          <view class="card-header">
            <view class="card-title-wrapper">
              <view class="card-icon i-mdi-lightbulb-outline"></view>
              <text class="card-title">参考答案</text>
            </view>
            <view class="toggle-answer-btn" @click="toggleAnswer">
              <view class="toggle-icon" :class="showAnswer ? 'i-mdi-eye-off' : 'i-mdi-eye'"></view>
              <text class="toggle-text">{{ showAnswer ? '隐藏答案' : '显示答案' }}</text>
            </view>
          </view>

          <!-- 答案内容 -->
          <view v-if="showAnswer" class="answer-content">
            <view class="answer-section">
              <view class="section-header">
                <view class="section-icon i-mdi-brain"></view>
                <text class="section-title">解题思路</text>
              </view>
              <rich-text
                v-if="renderedAnswer"
                class="markdown-content"
                :nodes="renderedAnswer"
              ></rich-text>
              <text v-else class="no-content-text">暂无解题思路</text>
            </view>

            <view class="answer-section">
              <view class="section-header">
                <view class="section-icon i-mdi-book-open"></view>
                <text class="section-title">知识点解析</text>
              </view>
              <rich-text
                v-if="renderedAnalysis"
                class="markdown-content"
                :nodes="renderedAnalysis"
              ></rich-text>
              <text v-else class="no-content-text">暂无知识点解析</text>
            </view>
          </view>

          <!-- 答案隐藏状态 -->
          <view v-else class="answer-hidden">
            <view class="hidden-icon i-mdi-lock"></view>
            <text class="hidden-text">答案已隐藏，点击"显示答案"按钮查看</text>
          </view>
        </view>

        <!-- 讨论区卡片 -->
        <view class="content-card">
          <view class="card-header">
            <view class="card-title-wrapper">
              <view class="card-icon i-mdi-forum"></view>
              <text class="card-title">讨论区 ({{ question.commentCount }})</text>
            </view>
            <view class="add-comment-btn" @click="toggleCommentInput">
              <view class="add-icon i-mdi-pencil"></view>
              <text class="add-text">发表评论</text>
            </view>
          </view>

          <!-- 评论输入框 -->
          <view v-if="showCommentInput" class="comment-input-wrapper">
            <textarea
              v-model="newComment"
              class="comment-input"
              placeholder="分享你的想法..."
              :maxlength="500"
              :adjust-position="true"
            />
            <view class="comment-actions">
              <text class="char-count">{{ newComment.length }}/500</text>
              <view class="action-buttons">
                <button class="cancel-btn" @click="toggleCommentInput">取消</button>
                <button class="submit-btn" @click="submitComment" :disabled="!newComment.trim()">
                  发表
                </button>
              </view>
            </view>
          </view>

          <!-- 评论列表 -->
          <view class="comments-list">
            <view v-for="comment in comments.slice(0, 3)" :key="comment.id" class="comment-item">
              <view class="comment-header">
                <view class="user-avatar">
                  <text class="avatar-emoji">{{ comment.avatar }}</text>
                </view>
                <view class="user-info">
                  <text class="user-name">{{ comment.author }}</text>
                  <text class="comment-time">{{ comment.time }}</text>
                </view>
              </view>
              <text class="comment-content">{{ comment.content }}</text>
              <view class="comment-actions">
                <view class="action-item" @click="likeComment(comment.id.toString())">
                  <view class="action-icon i-mdi-thumb-up-outline"></view>
                  <text class="action-text">{{ comment.likes }}</text>
                </view>
                <view class="action-item">
                  <view class="action-icon i-mdi-reply"></view>
                  <text class="action-text">回复</text>
                </view>
              </view>
            </view>
          </view>

          <!-- 查看更多评论 -->
          <view v-if="question.commentCount > 3" class="view-more-btn" @click="viewAllComments">
            <text class="view-more-text">查看全部 {{ question.commentCount }} 条评论</text>
          </view>
        </view>

        <!-- 题目导航 -->
        <view v-if="totalQuestions > 1" class="question-navigation">
          <view class="nav-header">
            <view class="nav-title">
              <view class="nav-icon i-mdi-format-list-numbered"></view>
              <text class="nav-text">题目导航</text>
            </view>
            <view class="nav-progress">
              <text class="progress-text">{{ questionIndex + 1 }} / {{ totalQuestions }}</text>
            </view>
          </view>

          <view class="nav-buttons">
            <view
              class="nav-btn prev-btn"
              :class="{ disabled: questionIndex <= 0 || isNavigating }"
              @click="goToPreviousQuestion"
            >
              <view class="btn-icon i-mdi-chevron-left"></view>
              <text class="btn-text">上一题</text>
            </view>

            <view class="nav-progress-bar">
              <view class="progress-track">
                <view
                  class="progress-fill"
                  :style="{ width: `${((questionIndex + 1) / totalQuestions) * 100}%` }"
                ></view>
              </view>
            </view>

            <view
              class="nav-btn next-btn"
              :class="{ disabled: questionIndex >= totalQuestions - 1 || isNavigating }"
              @click="goToNextQuestion"
            >
              <text class="btn-text">下一题</text>
              <view class="btn-icon i-mdi-chevron-right"></view>
            </view>
          </view>
        </view>

        <!-- 底部操作按钮 -->
        <view class="bottom-actions">
          <view class="primary-btn" @click="startPractice">
            <view class="btn-icon i-mdi-play"></view>
            <text class="btn-text">开始练习</text>
          </view>
          <view class="secondary-btn" @click="toggleBookmark">
            <view
              class="btn-icon"
              :class="question.isBookmarked ? 'i-mdi-bookmark' : 'i-mdi-bookmark-outline'"
            ></view>
            <text class="btn-text">{{ question.isBookmarked ? '已收藏' : '收藏' }}</text>
          </view>
        </view>

        <!-- 学习时长记录状态指示器 -->
        <view v-if="isTimerActive" class="study-timer-indicator">
          <view class="timer-icon i-mdi-clock-outline"></view>
          <text class="timer-text">正在记录学习时长...</text>
        </view>
      </view>
    </scroll-view>
  </view>
</template>

<style lang="scss" scoped>
/* 页面主容器 */
.question-detail-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
}

/* 主滚动容器 */
.main-scroll {
  background: transparent;
}

/* 内容容器 */
.content-container {
  padding: 32rpx 24rpx 120rpx;
}
/* 通用卡片样式 */
.content-card {
  margin-bottom: 32rpx;
  overflow: hidden;
  background: #ffffff;
  border-radius: 24rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 201, 167, 0.08);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  &:active {
    box-shadow: 0 16rpx 48rpx rgba(0, 201, 167, 0.12);
    transform: translateY(-4rpx);
  }
}
.no-content-text {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  display: block;
  font-size: 28rpx;
  line-height: 1.6;
  color: #94a3b8;
  white-space: pre-line;
}
/* 卡片头部 */
.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx 32rpx 24rpx;
  border-bottom: 2rpx solid #f1f5f9;

  .card-title-wrapper {
    display: flex;
    gap: 12rpx;
    align-items: center;

    .card-icon {
      font-size: 32rpx;
      color: #00c9a7;
    }
  }

  .card-title {
    font-size: 32rpx;
    font-weight: bold;
    color: #1e293b;
  }
}
/* 题目头部卡片特殊样式 */
.question-header-card {
  padding: 32rpx;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border: 2rpx solid #e2e8f0;

  .header-top {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 24rpx;
  }

  .difficulty-tag {
    padding: 12rpx 24rpx;
    font-size: 24rpx;
    font-weight: 600;
    border-radius: 24rpx;

    .difficulty-text {
      color: inherit;
    }
  }

  .difficulty-easy {
    color: #16a34a;
    background: linear-gradient(135deg, #dcfce7, #bbf7d0);
  }

  .difficulty-medium {
    color: #d97706;
    background: linear-gradient(135deg, #fef3c7, #fde68a);
  }

  .difficulty-hard {
    color: #dc2626;
    background: linear-gradient(135deg, #fee2e2, #fecaca);
  }

  .difficulty-default {
    color: #6b7280;
    background: linear-gradient(135deg, #f3f4f6, #e5e7eb);
  }

  .action-buttons {
    display: flex;
    gap: 16rpx;
  }

  .action-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 64rpx;
    height: 64rpx;
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(10rpx);
    border: 2rpx solid rgba(0, 201, 167, 0.2);
    border-radius: 50%;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

    .action-icon {
      font-size: 28rpx;
      color: #64748b;
    }

    &:active {
      transform: scale(0.9);
    }

    &.active {
      background: linear-gradient(135deg, #fbbf24, #f59e0b);
      border-color: #f59e0b;

      .action-icon {
        color: #ffffff !important;
      }
    }
  }

  .question-title {
    margin-bottom: 24rpx;

    .title-text {
      display: block;
      font-size: 36rpx;
      font-weight: bold;
      line-height: 1.4;
      color: #1e293b;
    }
  }

  .stats-row {
    display: flex;
    flex-wrap: wrap;
    gap: 32rpx;
    margin-bottom: 24rpx;
  }

  .stat-item {
    display: flex;
    gap: 8rpx;
    align-items: center;

    .stat-icon {
      font-size: 24rpx;
      color: #00c9a7;
    }

    .stat-text {
      font-size: 24rpx;
      color: #64748b;
    }

    &.study-time-item {
      .stat-icon {
        color: #f59e0b;
        animation: pulse 2s infinite;
      }

      .stat-text {
        color: #f59e0b;
        font-weight: 600;
      }
    }
  }

  .tags-container {
    display: flex;
    flex-wrap: wrap;
    gap: 12rpx;
  }

  .tag-item {
    padding: 8rpx 16rpx;
    background: linear-gradient(135deg, #dbeafe, #bfdbfe);
    border-radius: 20rpx;

    .tag-text {
      font-size: 22rpx;
      font-weight: 500;
      color: #2563eb;
    }
  }
}
/* 题目内容 */
.question-content {
  padding: 24rpx 32rpx 32rpx;

  .content-text {
    display: block;
    font-size: 28rpx;
    line-height: 1.6;
    color: #374151;
    white-space: pre-line;
  }
}
/* 答案切换按钮 */
.toggle-answer-btn {
  display: flex;
  gap: 8rpx;
  align-items: center;
  padding: 12rpx 20rpx;
  background: linear-gradient(135deg, #00c9a7, #4fd1c7);
  border-radius: 20rpx;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  .toggle-icon {
    font-size: 24rpx;
    color: #ffffff;
  }

  .toggle-text {
    font-size: 24rpx;
    font-weight: 500;
    color: #ffffff;
  }

  &:active {
    background: linear-gradient(135deg, #059669, #00c9a7);
    transform: scale(0.95);
  }
}
/* 答案内容 */
.answer-content {
  padding: 24rpx 32rpx 32rpx;
  animation: fadeIn 0.4s ease-out;
}

.answer-section {
  margin-bottom: 32rpx;

  &:last-child {
    margin-bottom: 0;
  }

  .section-header {
    display: flex;
    gap: 12rpx;
    align-items: center;
    margin-bottom: 16rpx;

    .section-icon {
      font-size: 28rpx;
      color: #00c9a7;
    }

    .section-title {
      font-size: 28rpx;
      font-weight: 600;
      color: #1e293b;
    }
  }

  .section-text {
    display: block;
    font-size: 26rpx;
    line-height: 1.6;
    color: #475569;
    white-space: pre-line;
  }
}
/* 答案隐藏状态 */
.answer-hidden {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60rpx 32rpx;
  background: linear-gradient(135deg, #f8fafc, #e2e8f0);

  .hidden-icon {
    margin-bottom: 16rpx;
    font-size: 48rpx;
    color: #94a3b8;
  }

  .hidden-text {
    font-size: 26rpx;
    color: #64748b;
    text-align: center;
  }
}
/* 评论输入框样式 */
.comment-input-wrapper {
  padding: 24rpx 32rpx;
  border-bottom: 2rpx solid #f1f5f9;
  animation: slideDown 0.3s ease-out;

  .comment-input {
    box-sizing: border-box;
    width: 100%;
    min-height: 120rpx;
    padding: 20rpx;
    font-size: 28rpx;
    line-height: 1.6;
    color: #1e293b;
    background: #f8fafc;
    border: 2rpx solid #e2e8f0;
    border-radius: 16rpx;
    transition: all 0.3s;

    &:focus {
      background: #ffffff;
      border-color: #00c9a7;
    }
  }

  .comment-actions {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 16rpx;

    .char-count {
      font-size: 24rpx;
      color: #94a3b8;
    }

    .action-buttons {
      display: flex;
      gap: 16rpx;

      .cancel-btn,
      .submit-btn {
        padding: 12rpx 32rpx;
        font-size: 26rpx;
        font-weight: 500;
        border: none;
        border-radius: 20rpx;
        transition: all 0.3s;
      }

      .cancel-btn {
        color: #64748b;
        background: #e2e8f0;

        &:active {
          background: #cbd5e1;
          transform: scale(0.95);
        }
      }

      .submit-btn {
        color: #ffffff;
        background: linear-gradient(135deg, #00c9a7, #4fd1c7);

        &:disabled {
          opacity: 0.5;
        }

        &:active:not(:disabled) {
          transform: scale(0.95);
        }
      }
    }
  }
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
/* 评论相关样式 */
.add-comment-btn {
  display: flex;
  gap: 8rpx;
  align-items: center;
  padding: 12rpx 20rpx;
  background: linear-gradient(135deg, #e0f2fe, #bfdbfe);
  border-radius: 20rpx;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  .add-icon {
    font-size: 24rpx;
    color: #2563eb;
  }

  .add-text {
    font-size: 24rpx;
    font-weight: 500;
    color: #2563eb;
  }

  &:active {
    background: linear-gradient(135deg, #bfdbfe, #93c5fd);
    transform: scale(0.95);
  }
}

.comments-list {
  padding: 24rpx 32rpx 0;
}

.comment-item {
  padding: 24rpx 0;
  border-bottom: 2rpx solid #f1f5f9;

  &:last-child {
    border-bottom: none;
  }

  .comment-header {
    display: flex;
    gap: 16rpx;
    align-items: center;
    margin-bottom: 16rpx;
  }

  .user-avatar {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 64rpx;
    height: 64rpx;
    background: linear-gradient(135deg, #00c9a7, #4fd1c7);
    border-radius: 50%;

    .avatar-emoji {
      font-size: 32rpx;
    }
  }

  .user-info {
    flex: 1;

    .user-name {
      display: block;
      margin-bottom: 4rpx;
      font-size: 26rpx;
      font-weight: 600;
      color: #1e293b;
    }

    .comment-time {
      font-size: 22rpx;
      color: #94a3b8;
    }
  }

  .comment-content {
    display: block;
    margin-bottom: 16rpx;
    font-size: 26rpx;
    line-height: 1.6;
    color: #475569;
  }

  .comment-actions {
    display: flex;
    gap: 32rpx;
  }

  .action-item {
    display: flex;
    gap: 8rpx;
    align-items: center;
    padding: 8rpx 16rpx;
    background: #f8fafc;
    border-radius: 16rpx;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

    &:active {
      background: #e2e8f0;
      transform: scale(0.95);
    }

    .action-icon {
      font-size: 20rpx;
      color: #64748b;
    }

    .action-text {
      font-size: 22rpx;
      color: #64748b;
    }
  }
}

.view-more-btn {
  padding: 24rpx 32rpx;
  text-align: center;
  background: linear-gradient(135deg, #f8fafc, #e2e8f0);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  .view-more-text {
    font-size: 26rpx;
    font-weight: 500;
    color: #64748b;
  }

  &:active {
    background: linear-gradient(135deg, #e2e8f0, #cbd5e1);
  }
}
/* 题目导航 */
.question-navigation {
  margin-bottom: 32rpx;
  padding: 32rpx;
  background: #ffffff;
  border-radius: 24rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
}

.nav-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}

.nav-title {
  display: flex;
  gap: 12rpx;
  align-items: center;
}

.nav-icon {
  font-size: 28rpx;
  color: #00c9a7;
}

.nav-text {
  font-size: 28rpx;
  font-weight: 600;
  color: #1e293b;
}

.nav-progress .progress-text {
  font-size: 24rpx;
  font-weight: 500;
  color: #64748b;
}

.nav-buttons {
  display: flex;
  gap: 24rpx;
  align-items: center;
}

.nav-btn {
  display: flex;
  gap: 8rpx;
  align-items: center;
  justify-content: center;
  padding: 20rpx 32rpx;
  background: linear-gradient(135deg, #00c9a7, #4fd1c7);
  border-radius: 20rpx;
  box-shadow: 0 6rpx 20rpx rgba(0, 201, 167, 0.3);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;

  .btn-icon {
    font-size: 24rpx;
    color: #ffffff;
  }

  .btn-text {
    font-size: 24rpx;
    font-weight: 500;
    color: #ffffff;
  }

  &:active:not(.disabled) {
    box-shadow: 0 8rpx 24rpx rgba(0, 201, 167, 0.4);
    transform: translateY(-2rpx) scale(0.98);
  }

  &.disabled {
    background: linear-gradient(135deg, #e2e8f0, #cbd5e1);
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
    cursor: not-allowed;

    .btn-icon,
    .btn-text {
      color: #94a3b8;
    }
  }
}

.nav-progress-bar {
  flex: 1;
  padding: 0 24rpx;
}

.progress-track {
  height: 8rpx;
  background: #e2e8f0;
  border-radius: 4rpx;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(135deg, #00c9a7, #4fd1c7);
  border-radius: 4rpx;
  transition: width 0.3s ease;
}

/* 底部操作按钮 */
.bottom-actions {
  display: flex;
  gap: 24rpx;
  padding: 32rpx 0;
}

.primary-btn {
  display: flex;
  flex: 1;
  gap: 12rpx;
  align-items: center;
  justify-content: center;
  padding: 24rpx;
  background: linear-gradient(135deg, #00c9a7, #4fd1c7);
  border-radius: 24rpx;
  box-shadow: 0 8rpx 24rpx rgba(0, 201, 167, 0.3);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  .btn-icon {
    font-size: 28rpx;
    color: #ffffff;
  }

  .btn-text {
    font-size: 28rpx;
    font-weight: 600;
    color: #ffffff;
  }

  &:active {
    box-shadow: 0 12rpx 32rpx rgba(0, 201, 167, 0.4);
    transform: translateY(-4rpx) scale(0.98);
  }
}

.secondary-btn {
  display: flex;
  gap: 8rpx;
  align-items: center;
  justify-content: center;
  padding: 24rpx 32rpx;
  background: linear-gradient(135deg, #fbbf24, #f59e0b);
  border-radius: 24rpx;
  box-shadow: 0 8rpx 24rpx rgba(251, 191, 36, 0.3);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  .btn-icon {
    font-size: 28rpx;
    color: #ffffff;
  }

  .btn-text {
    font-size: 26rpx;
    font-weight: 600;
    color: #ffffff;
  }

  &:active {
    box-shadow: 0 12rpx 32rpx rgba(251, 191, 36, 0.4);
    transform: translateY(-4rpx) scale(0.98);
  }
}

/* 学习时长记录状态指示器 */
.study-timer-indicator {
  position: fixed;
  bottom: 120rpx;
  right: 32rpx;
  z-index: 999;
  display: flex;
  gap: 8rpx;
  align-items: center;
  padding: 12rpx 20rpx;
  background: linear-gradient(135deg, rgba(0, 201, 167, 0.9), rgba(79, 209, 199, 0.9));
  backdrop-filter: blur(10rpx);
  border-radius: 24rpx;
  box-shadow: 0 8rpx 24rpx rgba(0, 201, 167, 0.3);
  animation: slideInRight 0.3s ease-out;

  .timer-icon {
    font-size: 24rpx;
    color: #ffffff;
    animation: pulse 2s infinite;
  }

  .timer-text {
    font-size: 22rpx;
    font-weight: 500;
    color: #ffffff;
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(100rpx);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}
/* 动画效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.1);
  }
}
/* 代码块样式 */
.content-section :deep(pre) {
  padding: 32rpx;
  margin: 24rpx 0;
  overflow-x: auto;
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  font-size: 24rpx;
  line-height: 1.6;
  background: #f8fafc;
  border: 2rpx solid #e2e8f0;
  border-radius: 16rpx;
}

.content-section :deep(code) {
  padding: 4rpx 8rpx;
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  font-size: 24rpx;
  color: #e11d48;
  background: #f1f5f9;
  border-radius: 8rpx;
}

.content-section :deep(pre code) {
  padding: 0;
  color: inherit;
  background: transparent;
  border-radius: 0;
}
/* H5端滚动条美化 */
/* stylelint-disable selector-type-no-unknown */
// #ifdef H5
:deep(scroll-view)::-webkit-scrollbar {
  width: 6rpx;
}

:deep(scroll-view)::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.05);
  border-radius: 3rpx;
}

:deep(scroll-view)::-webkit-scrollbar-thumb {
  background: rgba(0, 201, 167, 0.6);
  border-radius: 3rpx;
  transition: background 0.3s;
}

:deep(scroll-view)::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 201, 167, 0.8);
}
/* stylelint-enable selector-type-no-unknown */
// #endif
</style>
