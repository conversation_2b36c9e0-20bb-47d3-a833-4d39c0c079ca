package org.dromara.system.domain.vo;

import io.github.linpeilie.AutoMapperConfig__138;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.SysOssConfig;
import org.dromara.system.domain.SysOssConfigToSysOssConfigVoMapper__67;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__138.class,
    uses = {SysOssConfigToSysOssConfigVoMapper__67.class},
    imports = {}
)
public interface SysOssConfigVoToSysOssConfigMapper__67 extends BaseMapper<SysOssConfigVo, SysOssConfig> {
}
