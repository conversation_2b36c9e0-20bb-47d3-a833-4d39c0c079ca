package org.dromara.system.domain.vo;

import io.github.linpeilie.AutoMapperConfig__138;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.SysDept;
import org.dromara.system.domain.SysDeptToSysDeptVoMapper__30;
import org.dromara.system.domain.bo.SysDeptBoToSysDeptMapper__30;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__138.class,
    uses = {SysDeptBoToSysDeptMapper__30.class,SysDeptToSysDeptVoMapper__30.class,SysDeptToSysDeptVoMapper__30.class},
    imports = {}
)
public interface SysDeptVoToSysDeptMapper__30 extends BaseMapper<SysDeptVo, SysDept> {
}
