package org.dromara.system.domain.vo;

import io.github.linpeilie.AutoMapperConfig__138;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.SysRole;
import org.dromara.system.domain.SysRoleToSysRoleVoMapper__30;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__138.class,
    uses = {SysRoleToSysRoleVoMapper__30.class},
    imports = {}
)
public interface SysRoleVoToSysRoleMapper__30 extends BaseMapper<SysRoleVo, SysRole> {
}
