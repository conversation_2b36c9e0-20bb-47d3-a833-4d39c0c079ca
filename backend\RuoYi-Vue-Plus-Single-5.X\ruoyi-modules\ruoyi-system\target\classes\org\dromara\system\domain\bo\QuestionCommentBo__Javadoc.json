{"doc": " 题目评论业务对象\n\n <AUTHOR>\n", "fields": [{"name": "commentId", "doc": " 评论ID\n"}, {"name": "questionId", "doc": " 题目ID\n"}, {"name": "userId", "doc": " 用户ID\n"}, {"name": "parentId", "doc": " 父评论ID（回复时使用）\n"}, {"name": "content", "doc": " 评论内容\n"}, {"name": "likeCount", "doc": " 点赞数\n"}, {"name": "replyCount", "doc": " 回复数\n"}, {"name": "status", "doc": " 状态（0正常 1删除）\n"}, {"name": "sort", "doc": " 排序\n"}, {"name": "ip<PERSON><PERSON><PERSON>", "doc": " IP地址\n"}, {"name": "remark", "doc": " 备注\n"}], "enumConstants": [], "methods": [], "constructors": []}