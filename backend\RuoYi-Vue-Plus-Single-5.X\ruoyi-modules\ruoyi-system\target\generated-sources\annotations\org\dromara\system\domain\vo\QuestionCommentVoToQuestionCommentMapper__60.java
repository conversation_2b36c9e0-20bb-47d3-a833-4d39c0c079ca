package org.dromara.system.domain.vo;

import io.github.linpeilie.AutoMapperConfig__138;
import io.github.linpeilie.BaseMapper;
import org.dromara.common.mybatis.core.domain.QuestionComment;
import org.dromara.common.mybatis.core.domain.QuestionCommentToQuestionCommentVoMapper__60;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__138.class,
    uses = {QuestionCommentToQuestionCommentVoMapper__60.class,QuestionCommentToQuestionCommentVoMapper__60.class},
    imports = {}
)
public interface QuestionCommentVoToQuestionCommentMapper__60 extends BaseMapper<QuestionCommentVo, QuestionComment> {
}
