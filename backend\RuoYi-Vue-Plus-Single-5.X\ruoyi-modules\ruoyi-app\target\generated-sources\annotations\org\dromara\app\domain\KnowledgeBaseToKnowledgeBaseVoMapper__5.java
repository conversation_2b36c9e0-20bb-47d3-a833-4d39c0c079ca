package org.dromara.app.domain;

import io.github.linpeilie.AutoMapperConfig__139;
import io.github.linpeilie.BaseMapper;
import org.dromara.app.domain.bo.KnowledgeBaseBoToKnowledgeBaseMapper__5;
import org.dromara.app.domain.vo.KnowledgeBaseVo;
import org.dromara.app.domain.vo.KnowledgeBaseVoToKnowledgeBaseMapper__5;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__139.class,
    uses = {KnowledgeBaseBoToKnowledgeBaseMapper__5.class,KnowledgeBaseVoToKnowledgeBaseMapper__5.class},
    imports = {}
)
public interface KnowledgeBaseToKnowledgeBaseVoMapper__5 extends BaseMapper<KnowledgeBase, KnowledgeBaseVo> {
}
