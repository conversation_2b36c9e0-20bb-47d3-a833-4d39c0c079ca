package org.dromara.system.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;
import org.dromara.app.domain.QuestionComment;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 题目评论视图对象
 *
 * <AUTHOR>
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = QuestionComment.class)
public class QuestionCommentVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 评论ID
     */
    @ExcelProperty(value = "评论ID")
    private Long commentId;

    /**
     * 题目ID
     */
    @ExcelProperty(value = "题目ID")
    private Long questionId;

    /**
     * 题目标题（关联查询）
     */
    @ExcelProperty(value = "题目标题")
    private String questionTitle;

    /**
     * 用户ID
     */
    @ExcelProperty(value = "用户ID")
    private Long userId;

    /**
     * 用户昵称（关联查询）
     */
    @ExcelProperty(value = "用户昵称")
    private String userName;

    /**
     * 用户头像（关联查询）
     */
    private String userAvatar;

    /**
     * 父评论ID（回复时使用）
     */
    @ExcelProperty(value = "父评论ID")
    private Long parentId;

    /**
     * 评论内容
     */
    @ExcelProperty(value = "评论内容")
    private String content;

    /**
     * 点赞数
     */
    @ExcelProperty(value = "点赞数")
    private Integer likeCount;

    /**
     * 回复数
     */
    @ExcelProperty(value = "回复数")
    private Integer replyCount;

    /**
     * 状态（0正常 1删除）
     */
    @ExcelProperty(value = "状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "sys_normal_disable")
    private String status;

    /**
     * 排序
     */
    @ExcelProperty(value = "排序")
    private Integer sort;

    /**
     * IP地址
     */
    @ExcelProperty(value = "IP地址")
    private String ipAddress;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;

    /**
     * 创建时间
     */
    @ExcelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 更新时间
     */
    @ExcelProperty(value = "更新时间")
    private Date updateTime;

    /**
     * 子评论列表（非数据库字段）
     */
    private List<QuestionCommentVo> children;

    /**
     * 是否点赞（非数据库字段）
     */
    private Boolean isLiked;

    /**
     * 评论层级（非数据库字段）
     */
    private Integer level;
}
