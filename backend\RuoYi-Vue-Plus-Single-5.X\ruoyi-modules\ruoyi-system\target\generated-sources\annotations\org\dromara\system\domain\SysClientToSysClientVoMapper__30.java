package org.dromara.system.domain;

import io.github.linpeilie.AutoMapperConfig__138;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.bo.SysClientBoToSysClientMapper__30;
import org.dromara.system.domain.vo.SysClientVo;
import org.dromara.system.domain.vo.SysClientVoToSysClientMapper__30;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__138.class,
    uses = {SysClientVoToSysClientMapper__30.class,SysClientBoToSysClientMapper__30.class},
    imports = {}
)
public interface SysClientToSysClientVoMapper__30 extends BaseMapper<SysClient, SysClientVo> {
}
