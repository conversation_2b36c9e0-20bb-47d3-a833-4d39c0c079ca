package org.dromara.app.domain;

import io.github.linpeilie.AutoMapperConfig__139;
import io.github.linpeilie.BaseMapper;
import org.dromara.app.domain.bo.FeedbackBoToFeedbackMapper__5;
import org.dromara.app.domain.vo.FeedbackVo;
import org.dromara.app.domain.vo.FeedbackVoToFeedbackMapper__5;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__139.class,
    uses = {FeedbackVoToFeedbackMapper__5.class,FeedbackBoToFeedbackMapper__5.class},
    imports = {}
)
public interface FeedbackToFeedbackVoMapper__5 extends BaseMapper<Feedback, FeedbackVo> {
}
