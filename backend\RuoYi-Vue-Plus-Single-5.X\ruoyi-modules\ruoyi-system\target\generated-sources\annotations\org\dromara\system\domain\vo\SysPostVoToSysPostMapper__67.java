package org.dromara.system.domain.vo;

import io.github.linpeilie.AutoMapperConfig__138;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.SysPost;
import org.dromara.system.domain.SysPostToSysPostVoMapper__67;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__138.class,
    uses = {SysPostToSysPostVoMapper__67.class},
    imports = {}
)
public interface SysPostVoToSysPostMapper__67 extends BaseMapper<SysPostVo, SysPost> {
}
