package org.dromara.system.domain.vo;

import io.github.linpeilie.AutoMapperConfig__138;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.SysDictType;
import org.dromara.system.domain.SysDictTypeToSysDictTypeVoMapper__67;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__138.class,
    uses = {SysDictTypeToSysDictTypeVoMapper__67.class},
    imports = {}
)
public interface SysDictTypeVoToSysDictTypeMapper__67 extends BaseMapper<SysDictTypeVo, SysDictType> {
}
