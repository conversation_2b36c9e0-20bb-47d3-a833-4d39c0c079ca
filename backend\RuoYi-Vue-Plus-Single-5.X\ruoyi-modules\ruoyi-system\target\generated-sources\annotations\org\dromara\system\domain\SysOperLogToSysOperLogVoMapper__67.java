package org.dromara.system.domain;

import io.github.linpeilie.AutoMapperConfig__138;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.bo.SysOperLogBoToSysOperLogMapper__67;
import org.dromara.system.domain.vo.SysOperLogVo;
import org.dromara.system.domain.vo.SysOperLogVoToSysOperLogMapper__67;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__138.class,
    uses = {SysOperLogBoToSysOperLogMapper__67.class,SysOperLogVoToSysOperLogMapper__67.class},
    imports = {}
)
public interface SysOperLogToSysOperLogVoMapper__67 extends BaseMapper<SysOperLog, SysOperLogVo> {
}
