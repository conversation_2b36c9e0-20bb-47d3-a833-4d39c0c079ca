package org.dromara.system.domain.vo;

import io.github.linpeilie.AutoMapperConfig__139;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.SysLogininfor;
import org.dromara.system.domain.SysLogininforToSysLogininforVoMapper__5;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__139.class,
    uses = {SysLogininforToSysLogininforVoMapper__5.class},
    imports = {}
)
public interface SysLogininforVoToSysLogininforMapper__5 extends BaseMapper<SysLogininforVo, SysLogininfor> {
}
