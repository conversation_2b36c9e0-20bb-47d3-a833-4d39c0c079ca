package org.dromara.app.domain.vo;

import io.github.linpeilie.AutoMapperConfig__139;
import io.github.linpeilie.BaseMapper;
import org.dromara.app.domain.InterviewMode;
import org.dromara.app.domain.InterviewModeToInterviewModeVoMapper__5;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__139.class,
    uses = {InterviewModeToInterviewModeVoMapper__5.class},
    imports = {}
)
public interface InterviewModeVoToInterviewModeMapper__5 extends BaseMapper<InterviewModeVo, InterviewMode> {
}
