package org.dromara.system.domain.vo;

import io.github.linpeilie.AutoMapperConfig__138;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.SysConfig;
import org.dromara.system.domain.SysConfigToSysConfigVoMapper__30;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__138.class,
    uses = {SysConfigToSysConfigVoMapper__30.class},
    imports = {}
)
public interface SysConfigVoToSysConfigMapper__30 extends BaseMapper<SysConfigVo, SysConfig> {
}
