package org.dromara.system.domain.vo;

import io.github.linpeilie.AutoMapperConfig__138;
import io.github.linpeilie.BaseMapper;
import org.dromara.common.mybatis.core.domain.Question;
import org.dromara.common.mybatis.core.domain.QuestionToQuestionVoMapper__62;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__138.class,
    uses = {QuestionToQuestionVoMapper__62.class},
    imports = {}
)
public interface QuestionVoToQuestionMapper__62 extends BaseMapper<QuestionVo, Question> {
}
