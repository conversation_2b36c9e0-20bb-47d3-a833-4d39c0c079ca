package org.dromara.system.domain.vo;

import io.github.linpeilie.AutoMapperConfig__138;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.SysClient;
import org.dromara.system.domain.SysClientToSysClientVoMapper__30;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__138.class,
    uses = {SysClientToSysClientVoMapper__30.class},
    imports = {}
)
public interface SysClientVoToSysClientMapper__30 extends BaseMapper<SysClientVo, SysClient> {
}
