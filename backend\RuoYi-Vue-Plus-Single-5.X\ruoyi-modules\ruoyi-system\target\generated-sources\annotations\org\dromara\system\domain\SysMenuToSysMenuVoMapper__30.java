package org.dromara.system.domain;

import io.github.linpeilie.AutoMapperConfig__138;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.bo.SysMenuBoToSysMenuMapper__30;
import org.dromara.system.domain.vo.SysMenuVo;
import org.dromara.system.domain.vo.SysMenuVoToSysMenuMapper__30;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__138.class,
    uses = {SysMenuVoToSysMenuMapper__30.class,SysMenuBoToSysMenuMapper__30.class},
    imports = {}
)
public interface SysMenuToSysMenuVoMapper__30 extends BaseMapper<SysMenu, SysMenuVo> {
}
