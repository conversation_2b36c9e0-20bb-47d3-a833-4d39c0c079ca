package org.dromara.system.domain;

import io.github.linpeilie.AutoMapperConfig__139;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.bo.SysDeptBoToSysDeptMapper__5;
import org.dromara.system.domain.vo.SysDeptVo;
import org.dromara.system.domain.vo.SysDeptVoToSysDeptMapper__5;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__139.class,
    uses = {SysDeptBoToSysDeptMapper__5.class,SysDeptVoToSysDeptMapper__5.class,SysDeptBoToSysDeptMapper__5.class},
    imports = {}
)
public interface SysDeptToSysDeptVoMapper__5 extends BaseMapper<SysDept, SysDeptVo> {
}
