package org.dromara.system.domain.vo;

import io.github.linpeilie.AutoMapperConfig__138;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.SysDictData;
import org.dromara.system.domain.SysDictDataToSysDictDataVoMapper__67;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__138.class,
    uses = {SysDictDataToSysDictDataVoMapper__67.class},
    imports = {}
)
public interface SysDictDataVoToSysDictDataMapper__67 extends BaseMapper<SysDictDataVo, SysDictData> {
}
