package org.dromara.common.mybatis.core.domain;

import javax.annotation.processing.Generated;
import org.dromara.system.domain.vo.QuestionVo;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-01T22:58:54+0800",
    comments = "version: 1.5.5.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250729-0351, environment: Java 21.0.8 (Eclipse Adoptium)"
)
@Component
public class QuestionToQuestionVoMapperImpl implements QuestionToQuestionVoMapper {

    @Override
    public QuestionVo convert(Question arg0) {
        if ( arg0 == null ) {
            return null;
        }

        QuestionVo questionVo = new QuestionVo();

        questionVo.setAcceptanceRate( arg0.getAcceptanceRate() );
        questionVo.setAnalysis( arg0.getAnalysis() );
        questionVo.setAnswer( arg0.getAnswer() );
        questionVo.setBankId( arg0.getBankId() );
        questionVo.setCategory( arg0.getCategory() );
        questionVo.setCommentCount( arg0.getCommentCount() );
        questionVo.setContent( arg0.getContent() );
        questionVo.setCorrectRate( arg0.getCorrectRate() );
        questionVo.setCreateTime( arg0.getCreateTime() );
        questionVo.setDescription( arg0.getDescription() );
        questionVo.setDifficulty( arg0.getDifficulty() );
        questionVo.setPracticeCount( arg0.getPracticeCount() );
        questionVo.setQuestionCode( arg0.getQuestionCode() );
        questionVo.setQuestionId( arg0.getQuestionId() );
        questionVo.setRemark( arg0.getRemark() );
        questionVo.setSort( arg0.getSort() );
        questionVo.setStatus( arg0.getStatus() );
        questionVo.setTags( arg0.getTags() );
        questionVo.setTitle( arg0.getTitle() );
        questionVo.setType( arg0.getType() );
        questionVo.setUpdateTime( arg0.getUpdateTime() );

        return questionVo;
    }

    @Override
    public QuestionVo convert(Question arg0, QuestionVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setAcceptanceRate( arg0.getAcceptanceRate() );
        arg1.setAnalysis( arg0.getAnalysis() );
        arg1.setAnswer( arg0.getAnswer() );
        arg1.setBankId( arg0.getBankId() );
        arg1.setCategory( arg0.getCategory() );
        arg1.setCommentCount( arg0.getCommentCount() );
        arg1.setContent( arg0.getContent() );
        arg1.setCorrectRate( arg0.getCorrectRate() );
        arg1.setCreateTime( arg0.getCreateTime() );
        arg1.setDescription( arg0.getDescription() );
        arg1.setDifficulty( arg0.getDifficulty() );
        arg1.setPracticeCount( arg0.getPracticeCount() );
        arg1.setQuestionCode( arg0.getQuestionCode() );
        arg1.setQuestionId( arg0.getQuestionId() );
        arg1.setRemark( arg0.getRemark() );
        arg1.setSort( arg0.getSort() );
        arg1.setStatus( arg0.getStatus() );
        arg1.setTags( arg0.getTags() );
        arg1.setTitle( arg0.getTitle() );
        arg1.setType( arg0.getType() );
        arg1.setUpdateTime( arg0.getUpdateTime() );

        return arg1;
    }
}
