package org.dromara.system.domain.vo;

import io.github.linpeilie.AutoMapperConfig__139;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.SysDictData;
import org.dromara.system.domain.SysDictDataToSysDictDataVoMapper__5;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__139.class,
    uses = {SysDictDataToSysDictDataVoMapper__5.class},
    imports = {}
)
public interface SysDictDataVoToSysDictDataMapper__5 extends BaseMapper<SysDictDataVo, SysDictData> {
}
