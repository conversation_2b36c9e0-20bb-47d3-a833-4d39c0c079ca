{"doc": " 题库Service业务层处理\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "queryById", "paramTypes": ["java.lang.Long"], "doc": " 查询题库\n"}, {"name": "queryList", "paramTypes": ["org.dromara.system.domain.bo.QuestionBankBo"], "doc": " 查询题库列表\n"}, {"name": "queryPageList", "paramTypes": ["org.dromara.system.domain.bo.QuestionBankBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 分页查询题库列表\n"}, {"name": "buildQueryWrapper", "paramTypes": ["org.dromara.system.domain.bo.QuestionBankBo"], "doc": " 构建查询条件\n"}, {"name": "insertByBo", "paramTypes": ["org.dromara.system.domain.bo.QuestionBankBo"], "doc": " 新增题库\n"}, {"name": "updateByBo", "paramTypes": ["org.dromara.system.domain.bo.QuestionBankBo"], "doc": " 修改题库\n"}, {"name": "validEntityBeforeSave", "paramTypes": ["QuestionBank"], "doc": " 保存前的数据校验\n"}, {"name": "deleteWithValidByIds", "paramTypes": ["java.util.Collection"], "doc": " 批量删除题库\n"}, {"name": "queryByBankCode", "paramTypes": ["java.lang.String"], "doc": " 根据题库编码查询题库\n"}, {"name": "queryByMajorId", "paramTypes": ["java.lang.Long"], "doc": " 根据专业ID查询题库列表\n"}, {"name": "queryBookmarkedBanks", "paramTypes": ["java.lang.Long"], "doc": " 查询用户收藏的题库列表\n"}, {"name": "queryHotBanks", "paramTypes": ["java.lang.Integer"], "doc": " 查询热门题库列表\n"}, {"name": "updatePracticeCount", "paramTypes": ["java.lang.Long"], "doc": " 更新题库练习次数\n"}, {"name": "updateTotalQuestions", "paramTypes": ["java.lang.Long"], "doc": " 更新题库题目总数\n"}, {"name": "checkBankCodeUnique", "paramTypes": ["org.dromara.system.domain.bo.QuestionBankBo"], "doc": " 检查题库编码是否唯一\n"}, {"name": "importQuestionBank", "paramTypes": ["java.util.List"], "doc": " 批量导入题库\n"}, {"name": "exportQuestionBank", "paramTypes": ["org.dromara.system.domain.bo.QuestionBankBo"], "doc": " 导出题库数据\n"}, {"name": "changeStatus", "paramTypes": ["java.lang.Long", "java.lang.String"], "doc": " 启用/停用题库\n"}, {"name": "copyQuestionBank", "paramTypes": ["java.lang.Long", "java.lang.String"], "doc": " 复制题库\n"}, {"name": "getStatistics", "paramTypes": ["java.lang.Long"], "doc": " 获取题库统计信息\n"}], "constructors": []}