package org.dromara.app.domain;

import io.github.linpeilie.AutoMapperConfig__139;
import io.github.linpeilie.BaseMapper;
import org.dromara.app.domain.bo.VideoCommentBoToVideoCommentMapper__5;
import org.dromara.app.domain.vo.VideoCommentVo;
import org.dromara.app.domain.vo.VideoCommentVoToVideoCommentMapper__5;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__139.class,
    uses = {VideoCommentBoToVideoCommentMapper__5.class,VideoCommentVoToVideoCommentMapper__5.class},
    imports = {}
)
public interface VideoCommentToVideoCommentVoMapper__5 extends BaseMapper<VideoComment, VideoCommentVo> {
}
