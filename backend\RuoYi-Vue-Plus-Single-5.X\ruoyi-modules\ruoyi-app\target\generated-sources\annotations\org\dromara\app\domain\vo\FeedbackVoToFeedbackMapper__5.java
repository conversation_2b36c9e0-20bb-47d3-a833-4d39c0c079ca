package org.dromara.app.domain.vo;

import io.github.linpeilie.AutoMapperConfig__139;
import io.github.linpeilie.BaseMapper;
import org.dromara.app.domain.Feedback;
import org.dromara.app.domain.FeedbackToFeedbackVoMapper__5;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__139.class,
    uses = {FeedbackToFeedbackVoMapper__5.class},
    imports = {}
)
public interface FeedbackVoToFeedbackMapper__5 extends BaseMapper<FeedbackVo, Feedback> {
}
