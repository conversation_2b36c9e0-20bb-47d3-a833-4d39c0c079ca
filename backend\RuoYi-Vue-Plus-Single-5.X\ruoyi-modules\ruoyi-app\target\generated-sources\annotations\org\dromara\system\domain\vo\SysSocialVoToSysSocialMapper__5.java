package org.dromara.system.domain.vo;

import io.github.linpeilie.AutoMapperConfig__139;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.SysSocial;
import org.dromara.system.domain.SysSocialToSysSocialVoMapper__5;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__139.class,
    uses = {SysSocialToSysSocialVoMapper__5.class},
    imports = {}
)
public interface SysSocialVoToSysSocialMapper__5 extends BaseMapper<SysSocialVo, SysSocial> {
}
