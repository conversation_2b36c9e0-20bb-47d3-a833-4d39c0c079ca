package org.dromara.system.domain;

import io.github.linpeilie.AutoMapperConfig__138;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.bo.SysSocialBoToSysSocialMapper__30;
import org.dromara.system.domain.vo.SysSocialVo;
import org.dromara.system.domain.vo.SysSocialVoToSysSocialMapper__30;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__138.class,
    uses = {SysSocialBoToSysSocialMapper__30.class,SysSocialVoToSysSocialMapper__30.class},
    imports = {}
)
public interface SysSocialToSysSocialVoMapper__30 extends BaseMapper<SysSocial, SysSocialVo> {
}
