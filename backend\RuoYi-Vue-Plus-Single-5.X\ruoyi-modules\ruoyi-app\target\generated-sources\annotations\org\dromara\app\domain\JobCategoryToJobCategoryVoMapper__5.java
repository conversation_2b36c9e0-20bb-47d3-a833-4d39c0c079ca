package org.dromara.app.domain;

import io.github.linpeilie.AutoMapperConfig__139;
import io.github.linpeilie.BaseMapper;
import org.dromara.app.domain.vo.JobCategoryVo;
import org.dromara.app.domain.vo.JobCategoryVoToJobCategoryMapper__5;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__139.class,
    uses = {JobCategoryVoToJobCategoryMapper__5.class},
    imports = {}
)
public interface JobCategoryToJobCategoryVoMapper__5 extends BaseMapper<JobCategory, JobCategoryVo> {
}
