package org.dromara.common.mybatis.core.domain;

import io.github.linpeilie.AutoMapperConfig__139;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.bo.QuestionBankBoToQuestionBankMapper__3;
import org.dromara.system.domain.vo.QuestionBankVo;
import org.dromara.system.domain.vo.QuestionBankVoToQuestionBankMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__139.class,
    uses = {QuestionBankVoToQuestionBankMapper.class,QuestionBankBoToQuestionBankMapper__3.class},
    imports = {}
)
public interface QuestionBankToQuestionBankVoMapper extends BaseMapper<QuestionBank, QuestionBankVo> {
}
