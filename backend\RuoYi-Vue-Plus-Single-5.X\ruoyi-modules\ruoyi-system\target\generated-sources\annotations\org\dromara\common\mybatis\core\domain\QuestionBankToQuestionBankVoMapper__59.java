package org.dromara.common.mybatis.core.domain;

import io.github.linpeilie.AutoMapperConfig__138;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.bo.QuestionBankBoToQuestionBankMapper__66;
import org.dromara.system.domain.vo.QuestionBankVo;
import org.dromara.system.domain.vo.QuestionBankVoToQuestionBankMapper__59;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__138.class,
    uses = {QuestionBankVoToQuestionBankMapper__59.class,QuestionBankBoToQuestionBankMapper__66.class},
    imports = {}
)
public interface QuestionBankToQuestionBankVoMapper__59 extends BaseMapper<QuestionBank, QuestionBankVo> {
}
