package org.dromara.system.domain.vo;

import io.github.linpeilie.AutoMapperConfig__138;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.SysNotice;
import org.dromara.system.domain.SysNoticeToSysNoticeVoMapper__67;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__138.class,
    uses = {SysNoticeToSysNoticeVoMapper__67.class},
    imports = {}
)
public interface SysNoticeVoToSysNoticeMapper__67 extends BaseMapper<SysNoticeVo, SysNotice> {
}
