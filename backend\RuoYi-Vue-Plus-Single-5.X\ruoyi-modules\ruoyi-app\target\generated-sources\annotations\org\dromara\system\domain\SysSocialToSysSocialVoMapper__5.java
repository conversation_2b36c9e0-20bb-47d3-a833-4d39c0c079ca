package org.dromara.system.domain;

import io.github.linpeilie.AutoMapperConfig__139;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.bo.SysSocialBoToSysSocialMapper__5;
import org.dromara.system.domain.vo.SysSocialVo;
import org.dromara.system.domain.vo.SysSocialVoToSysSocialMapper__5;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__139.class,
    uses = {SysSocialBoToSysSocialMapper__5.class,SysSocialVoToSysSocialMapper__5.class},
    imports = {}
)
public interface SysSocialToSysSocialVoMapper__5 extends BaseMapper<SysSocial, SysSocialVo> {
}
