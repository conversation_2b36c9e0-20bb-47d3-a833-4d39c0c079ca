package org.dromara.system.domain.vo;

import io.github.linpeilie.AutoMapperConfig__139;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.SysOssConfig;
import org.dromara.system.domain.SysOssConfigToSysOssConfigVoMapper__5;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__139.class,
    uses = {SysOssConfigToSysOssConfigVoMapper__5.class},
    imports = {}
)
public interface SysOssConfigVoToSysOssConfigMapper__5 extends BaseMapper<SysOssConfigVo, SysOssConfig> {
}
