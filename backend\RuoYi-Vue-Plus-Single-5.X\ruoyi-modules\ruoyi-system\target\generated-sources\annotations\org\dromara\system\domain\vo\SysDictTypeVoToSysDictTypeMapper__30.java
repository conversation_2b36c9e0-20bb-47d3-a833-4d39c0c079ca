package org.dromara.system.domain.vo;

import io.github.linpeilie.AutoMapperConfig__138;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.SysDictType;
import org.dromara.system.domain.SysDictTypeToSysDictTypeVoMapper__30;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__138.class,
    uses = {SysDictTypeToSysDictTypeVoMapper__30.class},
    imports = {}
)
public interface SysDictTypeVoToSysDictTypeMapper__30 extends BaseMapper<SysDictTypeVo, SysDictType> {
}
