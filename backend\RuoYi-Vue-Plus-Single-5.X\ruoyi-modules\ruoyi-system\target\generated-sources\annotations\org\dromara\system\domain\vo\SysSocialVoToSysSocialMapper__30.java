package org.dromara.system.domain.vo;

import io.github.linpeilie.AutoMapperConfig__138;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.SysSocial;
import org.dromara.system.domain.SysSocialToSysSocialVoMapper__30;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__138.class,
    uses = {SysSocialToSysSocialVoMapper__30.class},
    imports = {}
)
public interface SysSocialVoToSysSocialMapper__30 extends BaseMapper<SysSocialVo, SysSocial> {
}
