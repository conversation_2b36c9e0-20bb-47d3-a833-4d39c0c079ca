package org.dromara.system.domain.vo;

import io.github.linpeilie.AutoMapperConfig__138;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.SysClient;
import org.dromara.system.domain.SysClientToSysClientVoMapper__67;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__138.class,
    uses = {SysClientToSysClientVoMapper__67.class},
    imports = {}
)
public interface SysClientVoToSysClientMapper__67 extends BaseMapper<SysClientVo, SysClient> {
}
