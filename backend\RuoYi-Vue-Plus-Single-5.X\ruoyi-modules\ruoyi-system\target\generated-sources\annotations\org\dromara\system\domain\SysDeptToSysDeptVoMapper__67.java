package org.dromara.system.domain;

import io.github.linpeilie.AutoMapperConfig__138;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.bo.SysDeptBoToSysDeptMapper__67;
import org.dromara.system.domain.vo.SysDeptVo;
import org.dromara.system.domain.vo.SysDeptVoToSysDeptMapper__67;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__138.class,
    uses = {SysDeptBoToSysDeptMapper__67.class,SysDeptVoToSysDeptMapper__67.class,SysDeptBoToSysDeptMapper__67.class},
    imports = {}
)
public interface SysDeptToSysDeptVoMapper__67 extends BaseMapper<SysDept, SysDeptVo> {
}
