package org.dromara.system.domain;

import io.github.linpeilie.AutoMapperConfig__138;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.bo.SysPostBoToSysPostMapper__30;
import org.dromara.system.domain.vo.SysPostVo;
import org.dromara.system.domain.vo.SysPostVoToSysPostMapper__30;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__138.class,
    uses = {SysPostBoToSysPostMapper__30.class,SysPostVoToSysPostMapper__30.class},
    imports = {}
)
public interface SysPostToSysPostVoMapper__30 extends BaseMapper<SysPost, SysPostVo> {
}
