package org.dromara.system.domain;

import io.github.linpeilie.AutoMapperConfig__139;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.bo.SysConfigBoToSysConfigMapper__5;
import org.dromara.system.domain.vo.SysConfigVo;
import org.dromara.system.domain.vo.SysConfigVoToSysConfigMapper__5;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__139.class,
    uses = {SysConfigVoToSysConfigMapper__5.class,SysConfigBoToSysConfigMapper__5.class},
    imports = {}
)
public interface SysConfigToSysConfigVoMapper__5 extends BaseMapper<SysConfig, SysConfigVo> {
}
