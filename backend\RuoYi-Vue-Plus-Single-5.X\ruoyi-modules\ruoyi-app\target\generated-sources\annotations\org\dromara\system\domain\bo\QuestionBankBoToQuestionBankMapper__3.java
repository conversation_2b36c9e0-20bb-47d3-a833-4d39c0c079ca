package org.dromara.system.domain.bo;

import io.github.linpeilie.AutoMapperConfig__139;
import io.github.linpeilie.BaseMapper;
import org.dromara.common.mybatis.core.domain.QuestionBank;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__139.class,
    uses = {},
    imports = {}
)
public interface QuestionBankBoToQuestionBankMapper__3 extends BaseMapper<QuestionBankBo, QuestionBank> {
}
