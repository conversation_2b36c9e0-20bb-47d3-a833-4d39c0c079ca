package org.dromara.app.domain.bo;

import io.github.linpeilie.AutoMapperConfig__139;
import io.github.linpeilie.BaseMapper;
import org.dromara.app.domain.KnowledgeDocument;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__139.class,
    uses = {},
    imports = {}
)
public interface KnowledgeDocumentBoToKnowledgeDocumentMapper__5 extends BaseMapper<KnowledgeDocumentBo, KnowledgeDocument> {
}
