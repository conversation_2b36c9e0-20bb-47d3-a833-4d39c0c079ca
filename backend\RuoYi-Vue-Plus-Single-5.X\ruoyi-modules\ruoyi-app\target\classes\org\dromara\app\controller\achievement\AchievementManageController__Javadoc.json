{"doc": "\n 成就管理Controller\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["org.dromara.common.mybatis.core.page.PageQuery"], "doc": "\n 查询成就列表\r\n"}, {"name": "export", "paramTypes": ["org.dromara.app.domain.bo.AchievementBo", "jakarta.servlet.http.HttpServletResponse"], "doc": "\n 导出成就列表\r\n"}, {"name": "getInfo", "paramTypes": ["java.lang.Long"], "doc": "\n 获取成就详细信息\r\n"}, {"name": "add", "paramTypes": ["org.dromara.app.domain.bo.AchievementBo"], "doc": "\n 新增成就\r\n"}, {"name": "edit", "paramTypes": ["org.dromara.app.domain.bo.AchievementBo"], "doc": "\n 修改成就\r\n"}, {"name": "remove", "paramTypes": ["java.lang.Long[]"], "doc": "\n 删除成就\r\n"}, {"name": "batchUpdateStatus", "paramTypes": ["org.dromara.app.controller.achievement.AchievementManageController.BatchUpdateStatusRequest"], "doc": "\n 批量更新成就状态\r\n"}, {"name": "copyAchievement", "paramTypes": ["java.lang.Long"], "doc": "\n 复制成就\r\n"}, {"name": "previewTriggerCondition", "paramTypes": ["org.dromara.app.controller.achievement.AchievementManageController.PreviewRequest"], "doc": "\n 预览成就触发条件\r\n"}, {"name": "testAchievementRule", "paramTypes": ["java.lang.Long", "org.dromara.app.controller.achievement.AchievementManageController.TestRuleRequest"], "doc": "\n 测试成就规则\r\n"}, {"name": "getAchievementStats", "paramTypes": [], "doc": "\n 获取成就统计信息\r\n"}, {"name": "getAchievementTypes", "paramTypes": [], "doc": "\n 获取成就类型列表\r\n"}, {"name": "getBehaviorTypes", "paramTypes": [], "doc": "\n 获取行为类型列表\r\n"}, {"name": "importData", "paramTypes": ["org.springframework.web.multipart.MultipartFile", "java.lang.Bo<PERSON>an"], "doc": "\n 导入成就数据\r\n"}, {"name": "importTemplate", "paramTypes": ["jakarta.servlet.http.HttpServletResponse"], "doc": "\n 获取导入模板\r\n"}], "constructors": []}