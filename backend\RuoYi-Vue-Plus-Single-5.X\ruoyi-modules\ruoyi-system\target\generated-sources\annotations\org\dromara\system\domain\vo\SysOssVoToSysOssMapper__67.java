package org.dromara.system.domain.vo;

import io.github.linpeilie.AutoMapperConfig__138;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.SysOss;
import org.dromara.system.domain.SysOssToSysOssVoMapper__67;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__138.class,
    uses = {SysOssToSysOssVoMapper__67.class},
    imports = {}
)
public interface SysOssVoToSysOssMapper__67 extends BaseMapper<SysOssVo, SysOss> {
}
