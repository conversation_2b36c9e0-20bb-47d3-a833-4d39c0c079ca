package org.dromara.system.domain.vo;

import io.github.linpeilie.AutoMapperConfig__138;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.SysDept;
import org.dromara.system.domain.SysDeptToSysDeptVoMapper__67;
import org.dromara.system.domain.bo.SysDeptBoToSysDeptMapper__67;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__138.class,
    uses = {SysDeptBoToSysDeptMapper__67.class,SysDeptToSysDeptVoMapper__67.class,SysDeptToSysDeptVoMapper__67.class},
    imports = {}
)
public interface SysDeptVoToSysDeptMapper__67 extends BaseMapper<SysDeptVo, SysDept> {
}
