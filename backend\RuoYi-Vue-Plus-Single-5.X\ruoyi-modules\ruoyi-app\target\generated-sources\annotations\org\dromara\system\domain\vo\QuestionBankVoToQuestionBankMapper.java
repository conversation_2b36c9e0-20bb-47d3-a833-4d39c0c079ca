package org.dromara.system.domain.vo;

import io.github.linpeilie.AutoMapperConfig__139;
import io.github.linpeilie.BaseMapper;
import org.dromara.common.mybatis.core.domain.QuestionBank;
import org.dromara.common.mybatis.core.domain.QuestionBankToQuestionBankVoMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__139.class,
    uses = {QuestionBankToQuestionBankVoMapper.class},
    imports = {}
)
public interface QuestionBankVoToQuestionBankMapper extends BaseMapper<QuestionBankVo, QuestionBank> {
}
