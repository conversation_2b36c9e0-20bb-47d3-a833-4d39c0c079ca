{"doc": " 题目Service业务层处理\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "queryById", "paramTypes": ["java.lang.Long"], "doc": " 查询题目\n"}, {"name": "queryList", "paramTypes": ["org.dromara.system.domain.bo.QuestionBo"], "doc": " 查询题目列表\n"}, {"name": "queryPageList", "paramTypes": ["org.dromara.system.domain.bo.QuestionBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 分页查询题目列表\n"}, {"name": "queryPageListByBankId", "paramTypes": ["java.lang.Long", "org.dromara.system.domain.bo.QuestionBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 根据题库ID分页查询题目列表\n"}, {"name": "buildQueryWrapper", "paramTypes": ["org.dromara.system.domain.bo.QuestionBo"], "doc": " 构建查询条件\n"}, {"name": "insertByBo", "paramTypes": ["org.dromara.system.domain.bo.QuestionBo"], "doc": " 新增题目\n"}, {"name": "updateByBo", "paramTypes": ["org.dromara.system.domain.bo.QuestionBo"], "doc": " 修改题目\n"}, {"name": "validEntityBeforeSave", "paramTypes": ["org.dromara.common.mybatis.core.domain.Question"], "doc": " 保存前的数据校验\n"}, {"name": "deleteWithValidByIds", "paramTypes": ["java.util.Collection"], "doc": " 批量删除题目\n"}, {"name": "queryByQuestionCode", "paramTypes": ["java.lang.String"], "doc": " 根据题目编码查询题目\n"}, {"name": "queryByBankId", "paramTypes": ["java.lang.Long"], "doc": " 根据题库ID查询题目列表\n"}, {"name": "queryByCategory", "paramTypes": ["java.lang.String"], "doc": " 根据分类查询题目列表\n"}, {"name": "queryByDifficulty", "paramTypes": ["java.lang.Integer"], "doc": " 根据难度查询题目列表\n"}, {"name": "queryByType", "paramTypes": ["java.lang.Integer"], "doc": " 根据题目类型查询题目列表\n"}, {"name": "queryBookmarkedQuestions", "paramTypes": ["java.lang.Long"], "doc": " 查询用户收藏的题目列表\n"}, {"name": "queryHotQuestions", "paramTypes": ["java.lang.Integer"], "doc": " 查询热门题目列表\n"}, {"name": "queryRandomQuestions", "paramTypes": ["java.lang.Long", "java.lang.Integer"], "doc": " 随机查询题目列表\n"}, {"name": "updatePracticeCount", "paramTypes": ["java.lang.Long"], "doc": " 更新题目练习次数\n"}, {"name": "updateCommentCount", "paramTypes": ["java.lang.Long"], "doc": " 更新题目评论数\n"}, {"name": "updateCorrectRate", "paramTypes": ["java.lang.Long", "java.lang.Integer"], "doc": " 更新题目正确率\n"}, {"name": "checkQuestionCodeUnique", "paramTypes": ["org.dromara.system.domain.bo.QuestionBo"], "doc": " 检查题目编码是否唯一\n"}, {"name": "importQuestion", "paramTypes": ["java.util.List"], "doc": " 批量导入题目\n"}, {"name": "exportQuestion", "paramTypes": ["org.dromara.system.domain.bo.QuestionBo"], "doc": " 导出题目数据\n"}, {"name": "changeStatus", "paramTypes": ["java.lang.Long", "java.lang.String"], "doc": " 启用/停用题目\n"}, {"name": "copyQuestion", "paramTypes": ["java.lang.Long", "java.lang.String"], "doc": " 复制题目\n"}, {"name": "deleteByBankId", "paramTypes": ["java.lang.Long"], "doc": " 根据题库ID删除题目\n"}, {"name": "countByBankId", "paramTypes": ["java.lang.Long"], "doc": " 统计题库下的题目数量\n"}], "constructors": []}