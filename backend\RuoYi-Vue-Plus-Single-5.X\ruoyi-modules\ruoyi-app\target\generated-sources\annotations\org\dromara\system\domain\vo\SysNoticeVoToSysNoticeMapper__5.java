package org.dromara.system.domain.vo;

import io.github.linpeilie.AutoMapperConfig__139;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.SysNotice;
import org.dromara.system.domain.SysNoticeToSysNoticeVoMapper__5;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__139.class,
    uses = {SysNoticeToSysNoticeVoMapper__5.class},
    imports = {}
)
public interface SysNoticeVoToSysNoticeMapper__5 extends BaseMapper<SysNoticeVo, SysNotice> {
}
