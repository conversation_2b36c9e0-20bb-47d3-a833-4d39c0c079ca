package org.dromara.app.domain.vo;

import io.github.linpeilie.AutoMapperConfig__139;
import io.github.linpeilie.BaseMapper;
import org.dromara.app.domain.Video;
import org.dromara.app.domain.VideoToVideoDetailVoMapper__5;
import org.dromara.app.utils.VideoMappingUtils;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__139.class,
    uses = {VideoMappingUtils.class,VideoToVideoDetailVoMapper__5.class},
    imports = {}
)
public interface VideoDetailVoToVideoMapper__5 extends BaseMapper<VideoDetailVo, Video> {
}
