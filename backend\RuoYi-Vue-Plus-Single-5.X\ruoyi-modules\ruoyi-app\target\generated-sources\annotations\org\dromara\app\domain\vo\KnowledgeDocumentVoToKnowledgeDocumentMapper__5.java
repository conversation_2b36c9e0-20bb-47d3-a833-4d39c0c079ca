package org.dromara.app.domain.vo;

import io.github.linpeilie.AutoMapperConfig__139;
import io.github.linpeilie.BaseMapper;
import org.dromara.app.domain.KnowledgeDocument;
import org.dromara.app.domain.KnowledgeDocumentToKnowledgeDocumentVoMapper__5;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__139.class,
    uses = {KnowledgeDocumentToKnowledgeDocumentVoMapper__5.class},
    imports = {}
)
public interface KnowledgeDocumentVoToKnowledgeDocumentMapper__5 extends BaseMapper<KnowledgeDocumentVo, KnowledgeDocument> {
}
