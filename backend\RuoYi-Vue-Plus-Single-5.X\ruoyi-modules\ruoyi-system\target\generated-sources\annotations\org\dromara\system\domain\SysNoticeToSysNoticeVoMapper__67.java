package org.dromara.system.domain;

import io.github.linpeilie.AutoMapperConfig__138;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.bo.SysNoticeBoToSysNoticeMapper__67;
import org.dromara.system.domain.vo.SysNoticeVo;
import org.dromara.system.domain.vo.SysNoticeVoToSysNoticeMapper__67;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__138.class,
    uses = {SysNoticeVoToSysNoticeMapper__67.class,SysNoticeBoToSysNoticeMapper__67.class},
    imports = {}
)
public interface SysNoticeToSysNoticeVoMapper__67 extends BaseMapper<SysNotice, SysNoticeVo> {
}
