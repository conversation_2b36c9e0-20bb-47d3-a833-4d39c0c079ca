package org.dromara.system.domain.vo;

import io.github.linpeilie.AutoMapperConfig__139;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.SysDictType;
import org.dromara.system.domain.SysDictTypeToSysDictTypeVoMapper__5;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__139.class,
    uses = {SysDictTypeToSysDictTypeVoMapper__5.class},
    imports = {}
)
public interface SysDictTypeVoToSysDictTypeMapper__5 extends BaseMapper<SysDictTypeVo, SysDictType> {
}
