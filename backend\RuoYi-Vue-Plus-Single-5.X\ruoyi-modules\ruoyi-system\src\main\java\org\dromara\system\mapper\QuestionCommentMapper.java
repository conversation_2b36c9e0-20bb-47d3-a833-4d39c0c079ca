package org.dromara.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.dromara.common.mybatis.annotation.DataPermission;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.system.domain.bo.QuestionCommentBo;
import org.dromara.system.domain.vo.QuestionCommentVo;
import org.dromara.app.domain.QuestionComment;

import java.util.Collection;
import java.util.List;

/**
 * 题目评论Mapper接口
 *
 * <AUTHOR>
 */
public interface QuestionCommentMapper extends BaseMapper<QuestionComment> {

    /**
     * 查询题目评论
     *
     * @param commentId 评论主键
     * @return 题目评论
     */
    QuestionCommentVo selectVoById(Long commentId);

    /**
     * 查询题目评论列表
     *
     * @param questionComment 题目评论
     * @return 题目评论集合
     */
    @DataPermission(key = "commentId")
    List<QuestionCommentVo> selectVoList(QuestionCommentBo questionComment);

    /**
     * 分页查询题目评论列表
     *
     * @param page            分页参数
     * @param questionComment 题目评论查询条件
     * @return 题目评论集合
     */
    @DataPermission(key = "commentId")
    Page<QuestionCommentVo> selectVoPage(Page<QuestionComment> page, @Param("ew") QuestionCommentBo questionComment);

    /**
     * 根据条件分页查询题目评论列表
     *
     * @param pageQuery       分页参数
     * @param questionComment 题目评论查询条件
     * @return 题目评论集合
     */
    @DataPermission(key = "commentId")
    Page<QuestionCommentVo> selectPageQuestionCommentList(@Param("page") PageQuery pageQuery, @Param("questionComment") QuestionCommentBo questionComment);

    /**
     * 查询题目评论列表
     *
     * @param commentIds 评论主键集合
     * @return 题目评论集合
     */
    List<QuestionCommentVo> selectVoByIds(@Param("commentIds") Collection<Long> commentIds);

    /**
     * 根据题目ID查询评论列表
     *
     * @param questionId 题目ID
     * @return 评论集合
     */
    List<QuestionCommentVo> selectVoByQuestionId(@Param("questionId") Long questionId);

    /**
     * 根据题目ID分页查询评论列表
     *
     * @param pageQuery       分页参数
     * @param questionId      题目ID
     * @param questionComment 评论查询条件
     * @return 评论集合
     */
    Page<QuestionCommentVo> selectPageCommentByQuestionId(@Param("page") PageQuery pageQuery, @Param("questionId") Long questionId, @Param("questionComment") QuestionCommentBo questionComment);

    /**
     * 根据用户ID查询评论列表
     *
     * @param userId 用户ID
     * @return 评论集合
     */
    List<QuestionCommentVo> selectVoByUserId(@Param("userId") Long userId);

    /**
     * 根据父评论ID查询子评论列表
     *
     * @param parentId 父评论ID
     * @return 子评论集合
     */
    List<QuestionCommentVo> selectVoByParentId(@Param("parentId") Long parentId);

    /**
     * 查询评论树形结构
     *
     * @param questionId 题目ID
     * @return 评论树形集合
     */
    List<QuestionCommentVo> selectCommentTree(@Param("questionId") Long questionId);

    /**
     * 查询热门评论列表
     *
     * @param questionId 题目ID
     * @param limit      限制数量
     * @return 评论集合
     */
    List<QuestionCommentVo> selectHotComments(@Param("questionId") Long questionId, @Param("limit") Integer limit);

    /**
     * 查询最新评论列表
     *
     * @param questionId 题目ID
     * @param limit      限制数量
     * @return 评论集合
     */
    List<QuestionCommentVo> selectLatestComments(@Param("questionId") Long questionId, @Param("limit") Integer limit);

    /**
     * 更新评论点赞数
     *
     * @param commentId 评论ID
     * @param increment 增量（可为负数）
     * @return 影响行数
     */
    int updateLikeCount(@Param("commentId") Long commentId, @Param("increment") Integer increment);

    /**
     * 更新评论回复数
     *
     * @param commentId 评论ID
     * @param increment 增量（可为负数）
     * @return 影响行数
     */
    int updateReplyCount(@Param("commentId") Long commentId, @Param("increment") Integer increment);

    /**
     * 批量删除题目评论
     *
     * @param commentIds 需要删除的评论主键集合
     * @return 影响行数
     */
    int deleteQuestionCommentByIds(@Param("commentIds") Collection<Long> commentIds);

    /**
     * 根据题目ID删除评论
     *
     * @param questionId 题目ID
     * @return 影响行数
     */
    int deleteCommentByQuestionId(@Param("questionId") Long questionId);

    /**
     * 根据用户ID删除评论
     *
     * @param userId 用户ID
     * @return 影响行数
     */
    int deleteCommentByUserId(@Param("userId") Long userId);

    /**
     * 统计题目下的评论数量
     *
     * @param questionId 题目ID
     * @return 评论数量
     */
    int countCommentsByQuestionId(@Param("questionId") Long questionId);

    /**
     * 统计用户的评论数量
     *
     * @param userId 用户ID
     * @return 评论数量
     */
    int countCommentsByUserId(@Param("userId") Long userId);
}
